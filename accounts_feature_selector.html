<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Selector</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .searchable-dropdown {
            position: relative;
            width: 100%;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .search-input:disabled {
            background-color: #f5f5f5;
            color: #666;
            cursor: not-allowed;
        }
        .dropdown-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .dropdown-option {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .dropdown-option:hover {
            background-color: #f5f5f5;
        }
        .dropdown-option:last-child {
            border-bottom: none;
        }
        .dropdown-option.selected {
            background-color: #007bff;
            color: white;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .selected-features {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .feature-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .remove-btn {
            background-color: #dc3545;
            padding: 5px 10px;
            font-size: 12px;
        }
        .remove-btn:hover {
            background-color: #c82333;
        }
        .json-output {
            margin-top: 20px;
            padding: 15px;
            background-color: #f1f1f1;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .option-selector {
            margin-top: 10px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .plan-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .plan-column {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .plan-column h4 {
            margin: 0 0 15px 0;
            text-align: center;
            color: #333;
            text-transform: uppercase;
            font-weight: bold;
        }
        .plan-column.micro h4 { color: #28a745; }
        .plan-column.small h4 { color: #17a2b8; }
        .plan-column.medium h4 { color: #ffc107; }
        .plan-column.enterprise h4 { color: #dc3545; }

        .plan-features {
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        .plan-feature-item {
            background: white;
            padding: 10px;
            margin: 8px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
            font-size: 12px;
            position: relative;
        }
        .plan-feature-item .remove-plan-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
        }
        .add-to-plan-btn {
            width: 100%;
            padding: 8px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-bottom: 5px;
        }
        .add-to-plan-btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Feature Selector</h1>
        
        <div class="form-group">
            <label for="groupSearch">Select Group:</label>
            <div class="searchable-dropdown">
                <input type="text" id="groupSearch" class="search-input" placeholder="Search groups..."
                       onfocus="showDropdown('group')" oninput="filterDropdown('group', this.value)">
                <div id="groupOptions" class="dropdown-options"></div>
            </div>
        </div>

        <div class="form-group">
            <label for="subgroupSearch">Select Subgroup:</label>
            <div class="searchable-dropdown">
                <input type="text" id="subgroupSearch" class="search-input" placeholder="Search subgroups..."
                       onfocus="showDropdown('subgroup')" oninput="filterDropdown('subgroup', this.value)" disabled>
                <div id="subgroupOptions" class="dropdown-options"></div>
            </div>
        </div>

        <div class="form-group">
            <label for="featureSearch">Select Feature:</label>
            <div class="searchable-dropdown">
                <input type="text" id="featureSearch" class="search-input" placeholder="Search features..."
                       onfocus="showDropdown('feature')" oninput="filterDropdown('feature', this.value)" disabled>
                <div id="featureOptions" class="dropdown-options"></div>
            </div>
        </div>

        <div id="optionSelector" class="option-selector" style="display: none;">
            <h4>Feature Options</h4>
            <label for="optionSearch">Select Option (if available):</label>
            <div class="searchable-dropdown">
                <input type="text" id="optionSearch" class="search-input" placeholder="Search options..."
                       onfocus="showDropdown('option')" oninput="filterDropdown('option', this.value)">
                <div id="optionOptions" class="dropdown-options"></div>
            </div>
        </div>

        <div class="plan-columns">
            <div class="plan-column micro">
                <h4>Micro Plan</h4>
                <button class="add-to-plan-btn" onclick="addToPlan('micro')">Add to Micro</button>
                <div class="plan-features" id="microFeatures"></div>
            </div>
            <div class="plan-column small">
                <h4>Small Plan</h4>
                <button class="add-to-plan-btn" onclick="addToPlan('small')">Add to Small</button>
                <div class="plan-features" id="smallFeatures"></div>
            </div>
            <div class="plan-column medium">
                <h4>Medium Plan</h4>
                <button class="add-to-plan-btn" onclick="addToPlan('medium')">Add to Medium</button>
                <div class="plan-features" id="mediumFeatures"></div>
            </div>
            <div class="plan-column enterprise">
                <h4>Enterprise Plan</h4>
                <button class="add-to-plan-btn" onclick="addToPlan('enterprise')">Add to Enterprise</button>
                <div class="plan-features" id="enterpriseFeatures"></div>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <button onclick="clearAll()">Clear All Plans</button>
            <button onclick="exportJSON()">Export JSON</button>
        </div>

        <div class="json-output" id="jsonOutput" style="display: none;"></div>
    </div>

    <script>
        // Sample data - Replace this with your actual data
        const sampleData = {
            groups: [{
              "name": "Consignee Collaboration Portal",
              "id": "53e2773180ccc8b9c8990f10d6d42d44",
              "sub_groups": []
            },
            {
              "name": "Freight Reconciliation & Accounting",
              "id": "28c58654a30213a92799af89eb85f6ec",
              "sub_groups": [
                {
                  "name": "Delivery Confirmation",
                  "id": "57dbbc24b1ad47b282b283e990808284"
                },
                {
                  "name": "Freight Reconciliation",
                  "id": "5a2f9835a8c24cdce9b0fac234d92291"
                },
                {
                  "name": "Freight Accounting",
                  "id": "a4a635a4d058c0f076da948837c6dff4"
                }
              ]
            },
            {
              "name": "Freight reconciliation and accounting configuration manager",
              "id": "36e0f42cd8030b8cada087755272b8fe",
              "sub_groups": []
            },
            {
              "name": "Freight reconciliation and accounting master data",
              "id": "46bc6ca2f7d1e02a38276eda761264bd",
              "sub_groups": []
            },
            {
              "name": "Infrastructure Access",
              "id": "8d96fe099efd8f6b104fc449088e78c6",
              "sub_groups": []
            },
            {
              "name": "LSP's Collaboration Portal",
              "id": "36293a6a573819268928c4dd849aa2b1",
              "sub_groups": []
            },
            {
              "name": "Logistics Modes Access",
              "id": "7f328fcb4553bb9c6a2187a1b2567f27",
              "sub_groups": []
            },
            {
              "name": "Logistics Movement type Access",
              "id": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
              "sub_groups": []
            },
            {
              "name": "Process, Audit & Compliance",
              "id": "5e9280ab188560b6861564be9209654c",
              "sub_groups": []
            },
            {
              "name": "Reports & Dashboards",
              "id": "afc16a82bf9d5efa09fc73750be0dc5b",
              "sub_groups": []
            },
            {
              "name": "SCLEN Integration",
              "id": "44021f9ab9d500d49e7d5f174a722761",
              "sub_groups": [
                {
                  "name": "Communication",
                  "id": "979b93285de87c4731fc824bd9e4ba75"
                },
                {
                  "name": "ERP's",
                  "id": "ec03c7f1556a54f400ce574f9dc6edbe"
                },
                {
                  "name": "Applications",
                  "id": "b5fba9ff24d0045d1377a05a46b32f68"
                },
                {
                  "name": "Tracking",
                  "id": "1879966223c3e382e14c6524c84942f1"
                },
                {
                  "name": "ULIP",
                  "id": "8cdf09d9d3f95cda969bbb47da8e8600"
                },
                {
                  "name": "Documentation",
                  "id": "55876228853abf632dec9346a4f372ec"
                },
                {
                  "name": "Hardware",
                  "id": "3ca14c518d1bf901acc339e7c9cd6d7f"
                }
              ]
            },
            {
              "name": "Support",
              "id": "2e116bc630724db3969160c7b4c0ed45",
              "sub_groups": []
            },
            {
              "name": "User Access",
              "id": "492f5b0022df505030d1324b0a101445",
              "sub_groups": []
            }],
            features: [{
  "id": "5306ab5f246b65e84fc6a0113176dd05",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "3 Way e-POD Matching",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "e965ea0ef1c195d9bddf602fd46e747b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ABB FAN",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "8a06d21513cdb5f385be30f6b893ca2b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "APP Tracking",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "3ecc3ea46d45fac6047d2c740a132254",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ARS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "26f375bda8d63cfe8406bb19106f16b5",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ASN notification portal",
  "gid": "1efab506f93c0e3645e2b96e0e79c45d",
  "gname": "Consignee collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "427a9487e154eb21b25edfa11773b2f4",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ASRS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "f615c83de9f6a40cc3294138209c9e86",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Activity checklist Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "4feb4f737a5ef647ae0bdcbb358e9b25",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Air - Domestic & EXIM",
  "gid": "7f328fcb4553bb9c6a2187a1b2567f27",
  "gname": "Logistics Modes Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "2f42e62bb31133097f64ed76394b2298",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Air Lines",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "837f89f7d713a7f6f5c9843570cb7819",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Analytics API's",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "8afd4a6610e46d7c7a4cd20283a64ac1",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Application type for Companies & Service providers",
  "gid": "492f5b0022df505030d1324b0a101445",
  "gname": "User Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Web & Mobile App",
      "ival_type": "text",
      "ival": "Web & Mobile App"
    }
  ]
},
{
  "id": "4dd46190581982492eb4474f447d045d",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Auto GRN ( Full or Partial) Workflow",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "57dbbc24b1ad47b282b283e990808284",
  "sgname": "Delivery Confirmation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "b588973b943c7dc2940d567e635d601a",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Auto Loaders",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "f028257f0dda86d08b691ed265fed19f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Auto SLA's verification",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "4c9ad470ba4e89442eb8ef22e67712e2",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Auto debit and Credit calculation",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "80d096275cb7894801c3287341aa9b86",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Auto invoice creation with digital signature",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "a4a635a4d058c0f076da948837c6dff4",
  "sgname": "Freight Accounting",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "6ab67bb1e9b9b3e1f61946ac1d3a0b7f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Boom barriers",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "fdc4c954c0ab5d0a09d364917bc5cd8f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "CO2 Emission Calculator",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "447004d45b24d4241692235564e386e8",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "CRM",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "af0a619fc6ceeeb247cdcccdb25d501c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Chart of Authority Master",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "60523f1f70869cc0ad410237f4a0e3a3",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Chart of authority (COA) Configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "f46a70887926f246a4eb2f52e5be89aa",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Cloud Infrastructure",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "6f844d2b4a9e571c8096891f9cd44bed",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Collaborative delivery confirmation portal",
  "gid": "1efab506f93c0e3645e2b96e0e79c45d",
  "gname": "Consignee collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "568fd4aa4cc05186c32d4fb26a41536e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Contract Rate Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "a0de8d9fbd7283e2542d852a32583f7d",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Credit Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "47ec5f330ec288b1cb2fce892639cca1",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Credit and debit Note creation",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "a4a635a4d058c0f076da948837c6dff4",
  "sgname": "Freight Accounting",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "359175cfe9774620f8ebcfacf894b80f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Custom Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "cc237eb5c3f17e512f3f9664af63e9da",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Customised Additional Dashboard",
  "gid": "afc16a82bf9d5efa09fc73750be0dc5b",
  "gname": "Reports & Dashboards",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "25000",
      "ival_type": "price",
      "ival": 25000
    },
    {
      "ilabel": "Custom",
      "ival_type": "text",
      "ival": "Custom"
    }
  ]
},
{
  "id": "44ebccd72e7285c32483818617b519cf",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Customised Additional MIS Report",
  "gid": "afc16a82bf9d5efa09fc73750be0dc5b",
  "gname": "Reports & Dashboards",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "10000",
      "ival_type": "price",
      "ival": 10000
    },
    {
      "ilabel": "Custom",
      "ival_type": "text",
      "ival": "Custom"
    }
  ]
},
{
  "id": "6d44c59deec86d7809ae339971bd2ca7",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "DMS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "8bb10ee8f26df8b215ffe157701cfc97",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Debit Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "2939456dcc64607d3db544bd2e67e4b0",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Debit and Credit rule engine configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "67b7c317f61c0b382b896b46f6b801cd",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Dedicated Account Manager",
  "gid": "2e116bc630724db3969160c7b4c0ed45",
  "gname": "Support",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "d0ba799328f32c31446d6055adb74051",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Digital e-POD - 3 Way closure",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "57dbbc24b1ad47b282b283e990808284",
  "sgname": "Delivery Confirmation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "9a3420149b301f30b54f7fed8a853cf3",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Discrepancy approval workflows",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "0c0f747abf3716773bd921eaf59b9c7c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Distance API",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "90289bb2e1da74f2b905fc2414af615b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "E-Mail",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "979b93285de87c4731fc824bd9e4ba75",
  "sgname": "Communication",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "25b2f4def025a3bf54a0ede3b94f292b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "E-Way bill",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "8cdf09d9d3f95cda969bbb47da8e8600",
  "sgname": "ULIP",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "e978562fee64b8e5678b087fed228a38",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Email Based Ticketing System",
  "gid": "2e116bc630724db3969160c7b4c0ed45",
  "gname": "Support",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "3a95b2f452caf6d0fb3ab38f56547fbe",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "End to End Audit Trail",
  "gid": "5e9280ab188560b6861564be9209654c",
  "gname": "Process, Audit & Compliance",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "411353cdf508fdc05c39d5cee4f413b9",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "FIOS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "8cdf09d9d3f95cda969bbb47da8e8600",
  "sgname": "ULIP",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "978084d2c66b15ea8fb72dcbd6ea621a",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "FNR Rake tracking",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "357a02cb42ef9d4ed1a3e84c424322c9",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "FasTag Tracking",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "f88a35a72e9a58e12c120f2c4262beaa",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Fastag",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "8cdf09d9d3f95cda969bbb47da8e8600",
  "sgname": "ULIP",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "5afdc3860ae99c2d866289f3b5b246ec",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Freight approval",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "1caef09d4ad92cc9afa28b5f49b9b0d6",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Freight reconciliation portal",
  "gid": "1efab506f93c0e3645e2b96e0e79c45d",
  "gname": "Consignee collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "39d0e582a89014019f6485e66e85bf78",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Fuel rate API's",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "d50b9c0c2ecc36037313c1a773ce7356",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "GPS & GNSS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "d5e929ae514822f1c101b032de560887",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Global currency API",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "4056b05b9eb247c8a893fd9b8a6e39a7",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Home grown ERP's",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "ec03c7f1556a54f400ce574f9dc6edbe",
  "sgname": "ERP's",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "5d5c2c51c9b8711a177427a1560ff12b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "IOT's",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "ea539560cb6b54965179a70c76dc460e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Inbound Movement",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "31dce6d69836d5510b12dead960739f4",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Indenting portal",
  "gid": "36293a6a573819268928c4dd849aa2b1",
  "gname": "LSP's Collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "1dd7ad453d9b5b13fcc9271b1be10419",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Industrial IOT's",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "bfc26671e668fd59e91cf3419ee675a0",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Inplant Movement",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "c54a4b2ca1519c11ec1a7e1f2f96f9b2",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Integrated Single window freight reconciliation dashboard",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "ce46ac5e0ee12c9d34ddb6ad9de76cd1",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Invoice approval stage visibility",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "a4a635a4d058c0f076da948837c6dff4",
  "sgname": "Freight Accounting",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "9d0230d23a6e716e9f6ac28bf8b1b2d3",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "LCL express services LSP's",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "541d1058996f8cc6a601aa0ab98cdd2c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "LSP' Freight reconciliation portal",
  "gid": "36293a6a573819268928c4dd849aa2b1",
  "gname": "LSP's Collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "0701227ba4e5646c6e36eb9e2efe5971",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "LSP's Master",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "645d159122b65ab63c4a33bf97370549",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Lane wise performa freight creation",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "5a2f9835a8c24cdce9b0fac234d92291",
  "sgname": "Freight Reconciliation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "f872d3d1748adfe737e559ad63099137",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Last Mile Distribution",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "d85411f53c358e979e3972c25299feff",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "MS Dynamics",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "ec03c7f1556a54f400ce574f9dc6edbe",
  "sgname": "ERP's",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "53ff363c2382ca446e54241efa0b321b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Mogilix",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "86d3b1a2b5762953bb38e1e082d956e7",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Multimodal - Domestic & EXIM",
  "gid": "7f328fcb4553bb9c6a2187a1b2567f27",
  "gname": "Logistics Modes Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "fa0383545313e93c547c36e2149d63ec",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Number of Users",
  "gid": "492f5b0022df505030d1324b0a101445",
  "gname": "User Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Unlimited",
      "ival_type": "text",
      "ival": "Unlimited"
    }
  ]
},
{
  "id": "c5c9eb79b81bdadd6d0462e29945724a",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "OR  Mathematical Solvers",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "b1dc3de9e0d164d6fd37b0e1474b302f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ORACLE",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "ec03c7f1556a54f400ce574f9dc6edbe",
  "sgname": "ERP's",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "32f378fe094491df13ba735e0c9320f5",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Outbound Movement",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "4eadad80bf57d13a409d1730f4720c2e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "PLMS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "a67e31b3b3a547568560b35cf8715fb4",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "POS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "eba94aed37cbc9de630e33ef1a6c468f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Payment confirmation workflow",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "a4a635a4d058c0f076da948837c6dff4",
  "sgname": "Freight Accounting",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "9f0e7e8fba2c997c02d3ceccbf50de6b",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Policies, KPI's & SLA's Configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "d98f4ebb911d6bd345e3a077e8658d53",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Policies, KPI's and KRA Rule Engines",
  "gid": "5e9280ab188560b6861564be9209654c",
  "gname": "Process, Audit & Compliance",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    },
    {
      "ilabel": "Custom",
      "ival_type": "text",
      "ival": "Custom"
    }
  ]
},
{
  "id": "deeb488c52d3905e56d4c6aeaa8a7f26",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Primary Distribution",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "1e0981fb3dad1f943862627ff121c578",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Process Deviation Logs",
  "gid": "5e9280ab188560b6861564be9209654c",
  "gname": "Process, Audit & Compliance",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "a22b788c13626b06bd0ab255c1bc849c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Procurement portal",
  "gid": "36293a6a573819268928c4dd849aa2b1",
  "gname": "LSP's Collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "34ae7a3b8b4a60c958b34accfacd51e5",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "RFID's",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "947fd72dc9101ffe6eca7d747ef7c77c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Rail - Domestic & EXIM",
  "gid": "7f328fcb4553bb9c6a2187a1b2567f27",
  "gname": "Logistics Modes Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "d5ef27e51153b4cb5639714db62a4584",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Real time shipment arrival alerts",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "57dbbc24b1ad47b282b283e990808284",
  "sgname": "Delivery Confirmation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "a06eccf0ad47d961c21807732b84072f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Road - Domestic & EXIM",
  "gid": "7f328fcb4553bb9c6a2187a1b2567f27",
  "gname": "Logistics Modes Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "816239e3a93608adc4c0c092537f78e4",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Route Master",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "11674553dad3e2efa6ccac66d16fa5b9",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SAP",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "ec03c7f1556a54f400ce574f9dc6edbe",
  "sgname": "ERP's",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "4941ae126b41e2aa3c6aac19691b6403",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SCLEN Network Configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "392510560c08b99143fe528bb51f233c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SCLEN Network Master's",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "1884169663755ce899be38f70379b2cf",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SIM",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "badab8e7b5c618dfc824793e793ee287",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SKU's Master",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "cd56cbe954882f83741bb16f233ac871",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SLA's deviation recording",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "57dbbc24b1ad47b282b283e990808284",
  "sgname": "Delivery Confirmation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "02dc4386e09c7beb2e8d3919cf6dab93",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SMS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "979b93285de87c4731fc824bd9e4ba75",
  "sgname": "Communication",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "6294c0a82e583e18b64784d1a8a756f9",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "SNOP",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "ae331926d45ecb6e678f7f4afc560ce7",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Sarathi",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "8cdf09d9d3f95cda969bbb47da8e8600",
  "sgname": "ULIP",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "0b52123e14b4c0fe74552f791ca3fa2c",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Scanners & Readers",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "8aed5d080f9495cc457e55dcc2e683db",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Secondary Distribution",
  "gid": "e8751d9af4a9c28c8f1d9b7f4b54ee3b",
  "gname": "Logistics Movement type Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "3c32fcd5bed364592e725fc5f11389c8",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Service Providers access",
  "gid": "492f5b0022df505030d1324b0a101445",
  "gname": "User Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Unlimited",
      "ival_type": "text",
      "ival": "Unlimited"
    }
  ]
},
{
  "id": "ed0edc871071abdf952305c1acc0242f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Shipment Tracking portal",
  "gid": "1efab506f93c0e3645e2b96e0e79c45d",
  "gname": "Consignee collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "34cf5419c329fd4eda3ee1f539b0dde8",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Shipment quality check and exception recording",
  "gid": "28c58654a30213a92799af89eb85f6ec",
  "gname": "Freight Reconciliation & Accounting",
  "sgid": "57dbbc24b1ad47b282b283e990808284",
  "sgname": "Delivery Confirmation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "79a33796a7d9e34256814215685aa8aa",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Shipping  Lines",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "1879966223c3e382e14c6524c84942f1",
  "sgname": "Tracking",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "80a60c34047286ea522efa9245e5c89e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Single Sign On (SSO)",
  "gid": "5e9280ab188560b6861564be9209654c",
  "gname": "Process, Audit & Compliance",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "7e15dfc0868d6aaae1cb955e6fcf8c2d",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Standard Dashboards",
  "gid": "afc16a82bf9d5efa09fc73750be0dc5b",
  "gname": "Reports & Dashboards",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "e857b45e6bba7a7ad0a6a8a3af0747bb",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Standard MIS Reports",
  "gid": "afc16a82bf9d5efa09fc73750be0dc5b",
  "gname": "Reports & Dashboards",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "3360814b546a698b5d3a31fb40ee36e1",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Supplier onboarding portal",
  "gid": "36293a6a573819268928c4dd849aa2b1",
  "gname": "LSP's Collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "8d2e5b7bb0ef2399a91eac4f817e3809",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Support Days",
  "gid": "2e116bc630724db3969160c7b4c0ed45",
  "gname": "Support",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Mon-Fri (9AM to 5PM)",
      "ival_type": "text",
      "ival": "Mon-Fri (9AM to 5PM)"
    },
    {
      "ilabel": "24X7",
      "ival_type": "text",
      "ival": "24X7"
    }
  ]
},
{
  "id": "b687a018ef1366cccdd414c80e7c981f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Tank Farm",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "9b54fa67191ea0d9b6f7f233f48db766",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Tracking portal",
  "gid": "36293a6a573819268928c4dd849aa2b1",
  "gname": "LSP's Collaboration Portal",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "39e9d63832492dd7714c90f4c79d010f",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Traffic Lights",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "6515b4af1ace86999130d27d691cc417",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "ULIP API's",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "733f525087eb28d068054327bcbbb2f6",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "User Master",
  "gid": "46bc6ca2f7d1e02a38276eda761264bd",
  "gname": "Freight reconciliation and accounting master data",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "001ee12064e8c1de23f05bca2d9faf10",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "User Profile Management",
  "gid": "492f5b0022df505030d1324b0a101445",
  "gname": "User Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "e56c4456d9a2195aa571ce1ad6f28ff3",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "User role configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "77235cff2f97b5733073725c469c5e1e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Vahan",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "8cdf09d9d3f95cda969bbb47da8e8600",
  "sgname": "ULIP",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "7b8b4db5106ae439c092b8427c1f25b6",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Vendor Portal",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "a9a90a8875fce674328082d81e2661c6",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Video telematics",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "e01d3111fb20dee58a4ec946aad5773e",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "WMS",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "b5fba9ff24d0045d1377a05a46b32f68",
  "sgname": "Applications",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "c931f59c1a6dbb4b520e814de64a7369",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Water - Domestic & EXIM",
  "gid": "7f328fcb4553bb9c6a2187a1b2567f27",
  "gname": "Logistics Modes Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "d93c4ec59e1084946532fb44b6c57199",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Weigh bridge",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "3ca14c518d1bf901acc339e7c9cd6d7f",
  "sgname": "Hardware",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "5be5cfa9458c63c153ad2b210d8c0454",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Whats App",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "979b93285de87c4731fc824bd9e4ba75",
  "sgname": "Communication",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "9bf8b7c044af97606c0ddf4d02f050a3",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Whats app Support",
  "gid": "2e116bc630724db3969160c7b4c0ed45",
  "gname": "Support",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    },
    {
      "ilabel": "No",
      "ival_type": "toggle",
      "ival": false
    }
  ]
},
{
  "id": "ed020dd8781c786e2ae673cf472c0248",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "Work flow configuration",
  "gid": "36e0f42cd8030b8cada087755272b8fe",
  "gname": "Freight reconciliation and accounting configuration manager",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "4f62480bdbb3550472a26e4a2577a2c9",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "e-Mail server",
  "gid": "8d96fe099efd8f6b104fc449088e78c6",
  "gname": "Infrastructure Access",
  "sgid": null,
  "sgname": null,
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
},
{
  "id": "22835558482857ac564117b4d0870c86",
  "is_cfg": false,
  "has_bl": false,
  "track_usage": false,
  "apply_disc": false,
  "is_addon": false,
  "addon_toggle": false,
  "name": "e-Mudhra",
  "gid": "44021f9ab9d500d49e7d5f174a722761",
  "gname": "SCLEN Integration",
  "sgid": "55876228853abf632dec9346a4f372ec",
  "sgname": "Documentation",
  "is_kf": false,
  "options": [
    {
      "ilabel": "Yes",
      "ival_type": "toggle",
      "ival": true
    }
  ]
}]
        };

        let planFeatures = {
            micro: [],
            small: [],
            medium: [],
            enterprise: []
        };
        let currentFeature = null;
        let selectedGroup = null;
        let selectedSubgroup = null;
        let selectedOption = null;
        let dropdownData = {
            group: [],
            subgroup: [],
            feature: [],
            option: []
        };

        // Initialize the page
        function init() {
            loadGroups();
            // Initialize empty plan displays
            updatePlanDisplay('micro');
            updatePlanDisplay('small');
            updatePlanDisplay('medium');
            updatePlanDisplay('enterprise');
        }

        function loadGroups() {
            dropdownData.group = sampleData.groups.map(group => ({
                id: group.id,
                text: group.name,
                data: group
            }));

            // Add click outside listener
            document.addEventListener('click', handleClickOutside);
        }

        function loadSubgroups() {
            if (!selectedGroup) return;

            // Reset dependent selections
            selectedSubgroup = null;
            currentFeature = null;
            selectedOption = null;
            dropdownData.feature = [];
            dropdownData.option = [];

            // Clear inputs
            document.getElementById('subgroupSearch').value = '';
            document.getElementById('featureSearch').value = '';
            hideFeatureOptions();

            // Check if group has subgroups
            if (selectedGroup.sub_groups && selectedGroup.sub_groups.length > 0) {
                // Group has subgroups - normal flow
                dropdownData.subgroup = selectedGroup.sub_groups.map(subgroup => ({
                    id: subgroup.id,
                    text: subgroup.name,
                    data: { subgroup, group: selectedGroup }
                }));

                document.getElementById('subgroupSearch').disabled = false;
                document.getElementById('subgroupSearch').placeholder = 'Search subgroups...';
                document.getElementById('featureSearch').disabled = true;
            } else {
                // Group has no subgroups - load features directly
                dropdownData.subgroup = [];
                document.getElementById('subgroupSearch').disabled = true;
                document.getElementById('subgroupSearch').placeholder = 'No subgroups available';

                // Load features directly for this group
                loadFeaturesForGroup();
            }
        }

        function loadFeaturesForGroup() {
            if (!selectedGroup) return;

            // Filter features by group (where sgid is null or empty - no subgroup)
            const groupFeatures = sampleData.features.filter(feature =>
                feature.gid === selectedGroup.id && (!feature.sgid || feature.sgid === null)
            );

            dropdownData.feature = groupFeatures.map(feature => ({
                id: feature.id,
                text: feature.name,
                data: feature
            }));

            // Reset dependent selections
            currentFeature = null;
            selectedOption = null;
            dropdownData.option = [];

            // Clear inputs and enable feature search
            document.getElementById('featureSearch').value = '';
            document.getElementById('featureSearch').disabled = false;
            document.getElementById('featureSearch').placeholder = 'Search features...';
            hideFeatureOptions();


        }

        function loadFeatures() {
            if (!selectedSubgroup) return;

            // Filter features by subgroup
            const subgroupFeatures = sampleData.features.filter(feature =>
                feature.sgid === selectedSubgroup.id
            );

            dropdownData.feature = subgroupFeatures.map(feature => ({
                id: feature.id,
                text: feature.name,
                data: feature
            }));

            // Reset dependent selections
            currentFeature = null;
            selectedOption = null;
            dropdownData.option = [];

            // Clear inputs
            document.getElementById('featureSearch').value = '';
            document.getElementById('featureSearch').disabled = false;
            hideFeatureOptions();
        }

        function showFeatureOptions() {
            if (!currentFeature) return;

            if (currentFeature.options && currentFeature.options.length > 0) {
                dropdownData.option = [
                    { id: '', text: '-- No specific option --', data: null },
                    ...currentFeature.options.map((option, index) => ({
                        id: index,
                        text: option.ilabel,
                        data: option
                    }))
                ];

                document.getElementById('optionSelector').style.display = 'block';
            } else {
                document.getElementById('optionSelector').style.display = 'block';
                dropdownData.option = [{ id: '', text: '-- No options available --', data: null }];
            }

            // Clear option input
            document.getElementById('optionSearch').value = '';
        }

        function clearOptionInputs() {
            document.getElementById('optionSearch').value = '';
            selectedOption = null;
        }

        function hideFeatureOptions() {
            document.getElementById('optionSelector').style.display = 'none';
            currentFeature = null;
        }

        // Searchable dropdown functions
        function showDropdown(type) {
            hideAllDropdowns();
            const optionsContainer = document.getElementById(`${type}Options`);
            if (dropdownData[type].length > 0) {
                renderDropdownOptions(type, dropdownData[type]);
                optionsContainer.style.display = 'block';
            }
        }

        function hideAllDropdowns() {
            ['group', 'subgroup', 'feature', 'option'].forEach(type => {
                document.getElementById(`${type}Options`).style.display = 'none';
            });
        }

        function filterDropdown(type, searchTerm) {
            const filteredData = dropdownData[type].filter(item =>
                item.text.toLowerCase().includes(searchTerm.toLowerCase())
            );
            renderDropdownOptions(type, filteredData);
            document.getElementById(`${type}Options`).style.display = 'block';
        }

        function renderDropdownOptions(type, data) {
            const optionsContainer = document.getElementById(`${type}Options`);
            optionsContainer.innerHTML = '';

            data.forEach(item => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'dropdown-option';
                optionDiv.textContent = item.text;
                optionDiv.onclick = () => selectDropdownOption(type, item);
                optionsContainer.appendChild(optionDiv);
            });
        }

        function selectDropdownOption(type, item) {
            const searchInput = document.getElementById(`${type}Search`);
            searchInput.value = item.text;
            hideAllDropdowns();

            if (type === 'group') {
                selectedGroup = item.data;
                loadSubgroups();
            } else if (type === 'subgroup') {
                selectedSubgroup = item.data.subgroup;
                loadFeatures();
            } else if (type === 'feature') {
                currentFeature = item.data;
                showFeatureOptions();
            } else if (type === 'option') {
                selectedOption = item.data;
            }
        }

        function handleClickOutside(event) {
            const isDropdownClick = event.target.closest('.searchable-dropdown');
            if (!isDropdownClick) {
                hideAllDropdowns();
            }
        }

        function addToPlan(planType) {
            if (!currentFeature) {
                alert('Please select a feature first');
                return;
            }

            // Create the feature object in the required format
            const featureToAdd = {
                gid: currentFeature.gid,
                gname: currentFeature.gname,
                sgid: currentFeature.sgid,
                sgname: currentFeature.sgname,
                id: currentFeature.id,
                name: currentFeature.name,
                is_kf: currentFeature.is_kf,
                is_addon: currentFeature.is_addon,
                addon_toggle: currentFeature.addon_toggle,
                is_cfg: currentFeature.is_cfg,
                has_bl: currentFeature.has_bl,
                track_usage: currentFeature.track_usage,
                apply_disc: currentFeature.apply_disc
            };

            // Add option data if selected
            if (selectedOption) {
                featureToAdd.ilabel = selectedOption.ilabel;
                featureToAdd.idata_type = selectedOption.idata_type;
                featureToAdd.ival_type = selectedOption.ival_type;
                featureToAdd.ival = selectedOption.ival;
            }

            // Check if feature with this option already exists in this plan
            const existingIndex = planFeatures[planType].findIndex(f =>
                f.id === featureToAdd.id &&
                f.ilabel === featureToAdd.ilabel
            );

            if (existingIndex === -1) {
                planFeatures[planType].push(featureToAdd);
                updatePlanDisplay(planType);

                // Don't reset selections - keep them for adding to other plans
                // clearOptionInputs();
            } else {
                alert(`This feature with the same option is already added to ${planType} plan`);
            }
        }

        function updatePlanDisplay(planType) {
            const planContainer = document.getElementById(`${planType}Features`);
            const features = planFeatures[planType];

            if (features.length === 0) {
                planContainer.innerHTML = '<p style="text-align: center; color: #666; font-style: italic;">No features added</p>';
                return;
            }

            planContainer.innerHTML = features.map((feature, index) => `
                <div class="plan-feature-item">
                    <button class="remove-plan-btn" onclick="removeFromPlan('${planType}', ${index})">×</button>
                    <strong>${feature.name}</strong><br>
                    <small>${feature.gname} > ${feature.sgname}</small>
                    ${feature.ilabel ? `<br><em>${feature.ilabel}</em>` : ''}
                </div>
            `).join('');
        }

        function removeFromPlan(planType, index) {
            planFeatures[planType].splice(index, 1);
            updatePlanDisplay(planType);
        }

        function clearAll() {
            if (confirm('Are you sure you want to clear all plans?')) {
                planFeatures = {
                    micro: [],
                    small: [],
                    medium: [],
                    enterprise: []
                };

                // Update all plan displays
                updatePlanDisplay('micro');
                updatePlanDisplay('small');
                updatePlanDisplay('medium');
                updatePlanDisplay('enterprise');

                document.getElementById('jsonOutput').style.display = 'none';
            }
        }

        function exportJSON() {
            const jsonOutput = document.getElementById('jsonOutput');
            jsonOutput.textContent = JSON.stringify(planFeatures, null, 2);
            jsonOutput.style.display = 'block';
        }

        // Initialize on page load
        window.onload = init;
    </script>
</body>
</html>
