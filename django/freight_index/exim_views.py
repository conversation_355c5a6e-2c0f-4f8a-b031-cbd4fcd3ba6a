import logging
from rest_framework import status
from pydantic import ValidationError
from rest_framework.views import APIView
from utils import (
    format_error_response,
    format_response
)
from .exim_utils import (
    get_freight_index,
    get_shipping_types,
    get_locations,
    get_regions
)
from .request_validators import EximCalculatePayloadValidator

logger = logging.getLogger('application')


class EximShippingTypesApi(APIView):

    def get(self, request, *args, **kwargs):
        params = request.GET

        include_transport_units = int(params.get('include_transport_units') or 0)
        include_bulk_sizes = int(params.get('include_bulk_sizes') or 0)
        search = str(params.get('search') or '')

        response, success = get_shipping_types(
            include_transport_units, include_bulk_sizes, search)

        if not success:
            try:
                errors = response['errors']
                if isinstance(errors, str):
                    error_msg = errors
                else:
                    try:
                        error_msg = errors['message']
                    except KeyError:
                        error_msg = ' '.join([errors[k] for k in errors])
            except (Attribute<PERSON>rror, ValueError, KeyError):
                error_msg = 'Something went wrong, please try again later'

            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                error_msg
            )
        else:
            return format_response(
                status.HTTP_200_OK,
                response['data'],
                'Shipping types retrieved successfully'
            )


class EximRegionsApi(APIView):

    def get(self, request, *args, **kwargs):
        params = request.GET

        include_children = int(params.get('include_children') or 0)
        include_parent = int(params.get('include_parent') or 0)
        search = str(params.get('search') or '')

        response, success = get_regions(
            include_parent, include_children, search)

        if not success:
            try:
                errors = response['errors']
                if isinstance(errors, str):
                    error_msg = errors
                else:
                    try:
                        error_msg = errors['message']
                    except KeyError:
                        error_msg = ' '.join([errors[k] for k in errors])
            except (AttributeError, ValueError, KeyError):
                error_msg = 'Something went wrong, please try again later'

            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                error_msg
            )
        else:
            return format_response(
                status.HTTP_200_OK,
                response['data'],
                'Regions retrieved successfully'
            )


class EximLocationsApi(APIView):

    def get(self, request, *args, **kwargs):
        params = request.GET

        location_type = str(params.get('location_type') or '')
        search = str(params.get('search') or '')

        if not (location_type and search):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Missing mandatory query params; required "
                "params: [location_type, search]"
            )

        response, success = get_locations(location_type, search)

        if not success:
            try:
                errors = response['errors']
                if isinstance(errors, str):
                    error_msg = errors
                else:
                    try:
                        error_msg = errors['message']
                    except KeyError:
                        error_msg = ' '.join([errors[k] for k in errors])
            except (AttributeError, ValueError, KeyError):
                error_msg = 'Something went wrong, please try again later'

            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                error_msg
            )
        else:
            return format_response(
                status.HTTP_200_OK,
                response['data'],
                'Locations retrieved successfully'
            )


class EximCalculateApi(APIView):

    def post(self, request, *args, **kwargs):
        raw_payload = request.data
        try:
            payload = EximCalculatePayloadValidator(**raw_payload).model_dump()
        except ValidationError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                pydantic_error=e
            )

        response, success = get_freight_index(payload)

        if not success:
            try:
                errors = response['errors']
                if isinstance(errors, str):
                    error_msg = errors
                else:
                    try:
                        error_msg = errors['message']
                    except KeyError:
                        error_msg = ' '.join([errors[k] for k in errors])
            except (AttributeError, ValueError, KeyError):
                error_msg = 'Something went wrong, please try again later'

            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                error_msg
            )
        else:
            return format_response(
                status.HTTP_200_OK,
                response['data'],
                'Freight index retrieved successfully'
            )

