import logging
import pandas as pd
from rest_framework import status
from rest_framework.views import APIView
from utils.mongo import MongoUtility
from throttle import CustomRateThrottle
from authn.decorators import reset_limit_on_error_response
from utils import (
    format_error_response,
    get_google_distance,
    format_response,
    Memoization,
    DateUtil
)
from utils.constants import (
    FreightIndexDBColls,
    FreightIndexModel,
    SAASModules
)
from .index_utils import (
    generate_predictions,
    fetch_encoded_value,
    get_distance
)
from authn import AuthenticatePostFirstAnonymousAccess

logger = logging.getLogger('application')


class PredictAPI(APIView):
    authentication_classes = (AuthenticatePostFirstAnonymousAccess, )

    @reset_limit_on_error_response()
    def post(self, request, *args, **kwargs):
        try:
            db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
            now = DateUtil.get_current_timestamp()

            payload = request.data
            from_city = payload.get("from_city", None)
            to_city = payload.get("to_city", None)
            from_state = payload.get("from_state", None)
            to_state = payload.get("to_state", None)
            body_type = int(payload.get("body_type", 0))
            contract_type = int(payload.get("contract_type", 0))
            business_type = int(payload.get("business_type", 0))
            capacity = float(payload.get("capacity", None))
            duration = int(payload.get("duration")) or 1

            if not all([from_city, to_city, from_state, to_state, body_type, contract_type, business_type, capacity]):
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Missing required parameters')

            location_master = list(
                db.find(FreightIndexDBColls.DISTANCE_MASTER, {}))
            location_master_df = pd.DataFrame(location_master)

            distance = get_distance(location_master_df, from_city, to_city)

            from_city_encoded, to_city_encoded, from_state_encoded, to_state_encoded = fetch_encoded_value(
                from_city, to_city, from_state, to_state)

            model = FreightIndexModel.LOCATION_BASED.value

            feature_names = [
                'distance', 'capacity',
                'from_loc_encoded', 'to_loc_encoded',
                'body_type_encoded', 'contract_type_encoded', 'business_type_encoded',
            ]

            input_data = [
                distance,
                capacity,
                to_city_encoded,
                from_city_encoded,
                body_type,
                contract_type,
                business_type,
            ]

            if not distance:
                model = FreightIndexModel.STATE_BASED.value
                distance = get_google_distance(SAASModules.FREIGHT_INDEX.value, from_city, to_city)

                if not distance:
                    return format_error_response(
                        status.HTTP_400_BAD_REQUEST,
                        "Distance not found via Google Maps."
                    )

                feature_names[2:4] = ['from_state', 'to_state']
                input_data[:4] = [distance, capacity, to_state_encoded, from_state_encoded]

            contract_tenure_prediction, monthly_prediction = generate_predictions(
                model, input_data, feature_names, now, contract_type, duration
            )

            response_data = {
                "contract_tenure_prediction": contract_tenure_prediction[0],
                "monthly_prediction": monthly_prediction,
                "distance": distance
            }
            return format_response(status.HTTP_200_OK, response_data, "Success")
        except Exception as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )


class DropdownsAPIView(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        params = request.GET

        try:
            dropdown_types = params.get('dropdown_type').split(',')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid dropdown_type recieved.',
            )

        data = {}
        for dropdown_type in dropdown_types:
            if hasattr(self, f'get_{dropdown_type}_dropdown'):
                dropdown_func = getattr(self, f'get_{dropdown_type}_dropdown')
            else:
                dropdown_func = self.get_dropdown_data

            data[dropdown_type] = dropdown_func(dropdown_type)

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_dropdown_data(self, dropdown_type):
        dropdown_coll = dropdown_type
        return Memoization.get_dropdown_data(dropdown_coll)


class LocationsAPIView(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        try:
            search_term = request.GET.get('search_term', '')
            db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
            location_master = list(db.find(FreightIndexDBColls.LOCATION_MASTER, {'city': {'$regex': search_term, '$options': 'i'}}))
            return format_response(status.HTTP_200_OK, location_master, 'Success')
        except Exception as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Oops something went Wrong."
            )
