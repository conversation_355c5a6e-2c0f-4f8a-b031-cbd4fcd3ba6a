import logging
from utils.constants import ApiType, SAASModules
from utils.memoization import Memoization
from utils.common import fetch_response

logger = logging.getLogger('application')


def get_shipping_types(include_transport_units, include_bulk_sizes, search):
    api_type = ApiType.SEA_RATES_SHIPPING_TYPES
    module_id = SAASModules.FREIGHT_INDEX.value
    api_config = Memoization.get_api_config(api_type, module_id)
    api_key = Memoization.get_api_key(
        module_id, service_key='sea_rates_api_key_be')

    params = {
        'include_transport_units': include_transport_units,
        'include_bulk_sizes': include_bulk_sizes
    }
    if search:
        params['search'] = search

    api_config['headers']['X-API-KEY'] = api_key

    response = fetch_response(
        api_config['api'], params,
        headers=api_config['headers'],
        method=api_config['method']
    )

    try:
        response_data = response.json()
    except Exception as e:
        logger.error(f'Failed to parse api({api_type.name}) response data: {e}')
        response_data = {}

    is_success = (response_data and ('errors' not in response_data))
    return response_data, is_success


def get_regions(include_parent, include_children, search):
    api_type = ApiType.SEA_RATES_REGIONS
    module_id = SAASModules.FREIGHT_INDEX.value
    api_config = Memoization.get_api_config(api_type, module_id)
    api_key = Memoization.get_api_key(
        module_id, service_key='sea_rates_api_key_be')

    params = {
        'include_children': include_children,
        'include_parent': include_parent,
    }
    if search:
        params['search'] = search

    api_config['headers']['X-API-KEY'] = api_key

    response = fetch_response(
        api_config['api'], params,
        headers=api_config['headers'],
        method=api_config['method']
    )

    try:
        response_data = response.json()
    except Exception as e:
        logger.error(f'Failed to parse api({api_type.name}) response data: {e}')
        response_data = {}

    is_success = (response_data and ('errors' not in response_data))
    return response_data, is_success


def get_locations(location_type, search, country=None): # available in premium
    api_type = ApiType.SEA_RATES_LOCATIONS
    module_id = SAASModules.FREIGHT_INDEX.value
    api_config = Memoization.get_api_config(api_type, module_id)
    api_key = Memoization.get_api_key(
        module_id, service_key='sea_rates_api_key_be')

    # location_type => Allowed values: COUNTRY CITY PORT AIRPORT
    params = {
        'location_type': location_type,
        'search': search
    }
    if country:
        params['country'] = country

    api_config['headers']['X-API-KEY'] = api_key

    response = fetch_response(
        api_config['api'], params,
        headers=api_config['headers'],
        method=api_config['method']
    )

    try:
        response_data = response.json()
    except Exception as e:
        logger.error(f'Failed to parse api({api_type.name}) response data: {e}')
        response_data = {}

    is_success = (response_data and ('errors' not in response_data))
    return response_data, is_success


def get_freight_index(payload):
    api_type = ApiType.SEA_RATES_FREIGHT_INDEX
    module_id = SAASModules.FREIGHT_INDEX.value
    api_config = Memoization.get_api_config(api_type, module_id)
    api_key = Memoization.get_api_key(
        module_id, service_key='sea_rates_api_key_be')

    api_config['headers']['X-API-KEY'] = api_key

    response = fetch_response(
        api_config['api'],
        headers=api_config['headers'],
        method=api_config['method'],
        payload=payload
    )

    try:
        response_data = response.json()
    except Exception as e:
        logger.error(f'Failed to parse api({api_type.name}) response data: {e}')
        response_data = {}

    is_success = (response_data and ('errors' not in response_data))
    return response_data, is_success
