import json
import boto3
import random
import logging
import pandas as pd
from django.conf import settings
from dateutil.relativedelta import relativedelta
from utils.constants import (
    ContractType, SAASModules, MLModelAction,
    FreightIndexDBColls, AdminDBColls
)
from utils import (
    DateUtil,
    Memoization,
    ConfigError,
    MongoUtility
)

logger = logging.getLogger('application')


BASE_DAY = 1
BASE_MONTH = 4  # APRIL
BASE_YEAR = 2023


def get_distance(distance_df, from_loc, to_loc):
    try:
        row = distance_df[
            (distance_df["from"].str.lower() == from_loc.lower()) &
            (distance_df["to"].str.lower() == to_loc.lower())
        ]
    except Exception:
        return 0

    if not row.empty:
        return float(row["DISTANCE"].values[0])
    return 0


def apply_yearly_inflation(predictions):
    inflated = []
    prev = predictions[0]
    inflated.append(round(prev, 2))
    for i in range(1, len(predictions)):
        growth = random.uniform(0.0001, 0.0005)
        prev = prev * (1 + growth)
        inflated.append(round(prev, 2))
    return inflated


def fetch_encoded_value(from_loc, to_loc, from_state, to_state):
    db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
    admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)

    states_list = list(admin_db.find(AdminDBColls.STATES_LIST, {}))
    state_master_df = pd.DataFrame(states_list)

    last_location_id = int(db.find(FreightIndexDBColls.LOCATION_MASTER, {}, sort=[("id", -1)], find_one=True)['id'])

    location_master = db.find(
        FreightIndexDBColls.LOCATION_MASTER,
        {
            "$or": [
                {"city": {"$regex": city, "$options": "i"}} for city in [from_loc, to_loc]
            ]
        }
    )
    location_master_df = pd.DataFrame(location_master)

    try:
        from_loc_details = location_master_df[location_master_df['city'].str.lower() == from_loc.lower()]
        from_loc_id = int(from_loc_details['id'].values[0])
        from_loc_state = from_loc_details['state'].values[0].lower()
        if from_loc_state != from_state.lower():
            raise ValueError("State mismatch.")

        from_loc_state = from_loc_details['state_code'].values[0]
    except Exception:
        from_loc_state = state_master_df[state_master_df['name'].str.lower() == from_state.lower()].astype(str)['id'].values[0]
        last_location_id += 1
        new_location = {
            "id": last_location_id,
            "city": from_loc,
            "state": from_state,
            "state_code": from_loc_state
        }
        db.insert(FreightIndexDBColls.LOCATION_MASTER, [new_location])
        from_loc_id = new_location['id']

    try:
        to_loc_details = location_master_df[location_master_df['city'].str.lower() == to_loc.lower()]
        to_loc_id = int(to_loc_details['id'].values[0])
        to_loc_state = to_loc_details['state'].values[0].lower()
        if to_loc_state != to_state.lower():
            raise ValueError("State mismatch.")

        to_loc_state = to_loc_details['state_code'].values[0]
    except Exception:
        to_loc_state = state_master_df[state_master_df['name'].str.lower() == to_state.lower()].astype(str)['id'].values[0]
        last_location_id += 1
        new_location = {
            "id": last_location_id,
            "city": to_loc,
            "state": to_state,
            "state_code": to_loc_state
        }
        db.insert(FreightIndexDBColls.LOCATION_MASTER, [new_location])
        to_loc_id = new_location['id']

    try:
        from_loc_state = int(state_master_df[state_master_df['name'].str.lower() == from_state.lower()].astype(str)['mis_id'].values[0])
        to_loc_state = int(state_master_df[state_master_df['name'].str.lower() == to_state.lower()].astype(str)['mis_id'].values[0])
    except Exception:
        from_state = db.find(AdminDBColls.STATES_LIST, {"name": {"$regex": from_state, "$options": "i"}}, find_one=True)
        to_state = db.find(AdminDBColls.STATES_LIST, {"name": {"$regex": to_state, "$options": "i"}}, find_one=True)
        from_loc_state = from_state["mis_id"]
        to_loc_state = to_state["mis_id"]

    return from_loc_id, to_loc_id, from_loc_state, to_loc_state


def fetch_sagemaker_response(model, payload, action):
    model_config = Memoization.get_sagemaker_model(SAASModules.FREIGHT_INDEX.value, model, action)

    if not model_config:
        raise ConfigError("Model not found")

    session = boto3.Session(
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION_NAME
    )
    sm_runtime = session.client("sagemaker-runtime")

    try:
        response = sm_runtime.invoke_endpoint(
            EndpointName=model_config['model_endpoint'],
            ContentType="application/json",
            Body=json.dumps(payload)
        )
    except Exception as e:
        logger.error(f"Sagemaker {action} failed with error: {str(e)}")
        raise NotImplementedError(f"Sagemaker {action} failed with error")

    response_body_bytes = response['Body'].read()
    response_text = response_body_bytes.decode('utf-8')

    try:
        result = json.loads(response_text)
    except json.JSONDecodeError:
        logger.error("Non-JSON response:", response_text)
        raise
    return result


def generate_predictions(model, input_values_list, feature_names, predict_date, contract_type, duration):
    prediction_date = DateUtil.convert_to_datetime(predict_date, "%Y-%m-%d")

    dates, month_dates, contract_tenure_records, monthly_records, base_records = [], [], [], [], []

    # Used for Monthly Prediction Graph
    date_pairs = [
        (prediction_date - relativedelta(months=i), prediction_date + relativedelta(months=i)) for i in range(duration + 1)
    ]

    all_dates = [dt for pair in date_pairs for dt in pair]
    month_dates = list(set(all_dates))

    monthly_records.extend([
        [dt.month, dt.year] + input_values_list
        for dt in month_dates
    ])

    # Used for Freight Index Graph
    base_records.extend([[BASE_MONTH, BASE_YEAR] + input_values_list])

    # Used for Contract Tenure Prediction Values, which requires grouping
    if contract_type in [ContractType.SPOT.value, ContractType.WEEKLY.value, ContractType.MONTHLY.value]:
        date_pairs = [
            (prediction_date - relativedelta(months=i), prediction_date + relativedelta(months=i)) for i in range(1)
        ]

        all_dates = [dt for pair in date_pairs for dt in pair]
        dates = list(set(all_dates))

        contract_tenure_records.extend([
            [dt.month, dt.year] + input_values_list
            for dt in dates
        ])

    elif contract_type == ContractType.QUARTERLY.value:
        for i in range(4):
            dt = prediction_date + relativedelta(months=i)
            dates.append(dt)
            record = [dt.month, dt.year] + input_values_list
            contract_tenure_records.append(record)

    elif contract_type == ContractType.HALF_YEARLY.value:
        for i in range(6):
            dt = prediction_date + relativedelta(months=i)
            dates.append(dt)
            record = [dt.month, dt.year] + input_values_list
            contract_tenure_records.append(record)

    elif contract_type == ContractType.YEARLY.value:
        for i in range(12):
            dt = prediction_date + relativedelta(months=i)
            dates.append(dt)
            record = [dt.month, dt.year] + input_values_list
            contract_tenure_records.append(record)
    else:
        raise ValueError("Selected Contract Type not supported at the moment.")

    base_period_df = pd.DataFrame(base_records, columns=['rfq_month', 'rfq_year'] + feature_names)
    contract_tenure_df = pd.DataFrame(contract_tenure_records, columns=['rfq_month', 'rfq_year'] + feature_names)
    monthly_df = pd.DataFrame(monthly_records, columns=['rfq_month', 'rfq_year'] + feature_names)

    try:
        base_period_result = fetch_sagemaker_response(model, base_period_df.to_dict(orient="records"), MLModelAction.PREDICT.name)
        contract_tenure_result = fetch_sagemaker_response(model, contract_tenure_df.to_dict(orient="records"), MLModelAction.PREDICT.name)
        monthly_result = fetch_sagemaker_response(model, monthly_df.to_dict(orient="records"), MLModelAction.PREDICT.name)

        base_period_df["Prediction"] = pd.DataFrame(base_period_result)['ptpk'].round(2).tolist()
        contract_tenure_df["Prediction"] = pd.DataFrame(contract_tenure_result)['ptpk'].round(2).tolist()
        monthly_df["Prediction"] = pd.DataFrame(monthly_result)['ptpk'].round(2).tolist()

        base_period_freight = pd.DataFrame(base_period_result)['ptpk'].round(2).tolist()[0]
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}")
        raise

    if contract_type in [ContractType.YEARLY.value, ContractType.QUARTERLY.value]:
        contract_tenure_df["Prediction"] = apply_yearly_inflation(contract_tenure_df["Prediction"].tolist())

    monthly_df["Date"] = [d.strftime("%B %Y") for d in month_dates]
    monthly_df["DISTANCE"] = input_values_list[0]
    monthly_df["vehicle_capacity_number"] = input_values_list[1]
    monthly_df["Rate_per_MT"] = ((monthly_df["Prediction"].round(2)) * monthly_df["DISTANCE"]).round(2)
    monthly_df["Rate_per_Trip"] = (monthly_df["Rate_per_MT"] * monthly_df["vehicle_capacity_number"]).round(2)
    monthly_df['base_period_freight'] = base_period_freight
    monthly_df["freight_index"] = ((monthly_df["Prediction"] / base_period_freight) * 100).astype(int)

    contract_tenure_df["Date"] = [d.strftime("%B %Y") for d in dates]
    contract_tenure_df["DISTANCE"] = input_values_list[0]
    contract_tenure_df["vehicle_capacity_number"] = input_values_list[1]
    contract_tenure_df["Rate_per_MT"] = ((contract_tenure_df["Prediction"].round(2)) * contract_tenure_df["DISTANCE"]).round(2)
    contract_tenure_df["Rate_per_Trip"] = contract_tenure_df["Rate_per_MT"] * contract_tenure_df["vehicle_capacity_number"]

    if contract_type == ContractType.QUARTERLY.value:
        def get_fiscal_quarter(month):
            if 4 <= month <= 6:
                return 1
            elif 7 <= month <= 9:
                return 2
            elif 10 <= month <= 12:
                return 3
            else:  # Jan–Mar
                return 4

        contract_tenure_df["FQ_Year"] = contract_tenure_df["rfq_year"]
        contract_tenure_df["FQ"] = contract_tenure_df["rfq_month"].apply(get_fiscal_quarter)
        contract_tenure_df.loc[contract_tenure_df["rfq_month"] < 4, "FQ_Year"] = contract_tenure_df["rfq_year"] - 1

        contract_tenure_df["FQ_Year"] = [d.year for d in dates]
        min_year = contract_tenure_df["FQ_Year"].min()
        min_quarter = contract_tenure_df["FQ"].min()
        contract_tenure_df["FQ_Year"] = min_year
        contract_tenure_df["FQ"] = min_quarter

        contract_tenure_df = contract_tenure_df.groupby(["FQ_Year", "FQ"]).agg({
            "Prediction": "mean",
            "Rate_per_MT": "mean",
            "Rate_per_Trip": "mean"
        }).reset_index()
        # contract_tenure_df["Date"] = contract_tenure_df.apply(lambda x: f"Q{int(x['FQ'])} - {int(x['FQ_Year'])}", axis=1)
    elif contract_type == ContractType.YEARLY.value:
        contract_tenure_df["Year"] = [d.year for d in dates]
        min_year = contract_tenure_df["Year"].min()
        contract_tenure_df["Year"] = min_year
        contract_tenure_df = contract_tenure_df.groupby("Year").agg({
            "Prediction": "mean",
            "Rate_per_MT": "mean",
            "Rate_per_Trip": "mean"
        }).reset_index()
        # contract_tenure_df["Date"] = contract_tenure_df["Year"].astype(str)
    else:
        contract_tenure_df["Month"] = prediction_date.month
        contract_tenure_df = contract_tenure_df.groupby("Month").agg({
            "Prediction": "mean",
            "Rate_per_MT": "mean",
            "Rate_per_Trip": "mean"
        }).reset_index()
        # contract_tenure_df["Date"] = contract_tenure_df["Month"].astype(str)

    contract_tenure_df["Prediction"] = contract_tenure_df["Prediction"].round(2).apply(lambda x: f"₹{float(x)}")
    contract_tenure_df["Rate_per_MT"] = contract_tenure_df["Rate_per_MT"].round(0).apply(lambda x: f"₹{int(x)}")
    contract_tenure_df["Rate_per_Trip"] = contract_tenure_df["Rate_per_Trip"].round(0).apply(lambda x: f"₹{int(x)}")

    contract_tenure_table_list = contract_tenure_df.to_dict(orient="records")
    monthly_table_list = monthly_df.to_dict(orient="records")
    monthly_sorted_table = sorted(monthly_table_list, key=lambda x: (x['rfq_year'], x['rfq_month']))
    return contract_tenure_table_list, monthly_sorted_table


def fetch_historic_freight(historic_freight_filter):
    db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
    admin_db = MongoUtility(module_id=SAASModules.ADMIN.value)
    try:
        from_city = historic_freight_filter['from']
        to_city = historic_freight_filter['to']

        cities = [from_city, to_city]
        body_types = historic_freight_filter['body_type']
        contract_types = historic_freight_filter['contract_type']
        business_types = historic_freight_filter['business_type']
        capacities = historic_freight_filter['vehicle_capacity_number']

        location_master = db.find(FreightIndexDBColls.LOCATION_MASTER, {
            "$or": [
                {"city": {"$regex": city, "$options": "i"}} for city in cities
            ]
        })

        if not location_master.count():
            raise ValueError(f"No location data found for cities: {', '.join(cities)}")

        body_type_data = admin_db.find(AdminDBColls.VEHICLE_BODY_TYPES, {"id": body_types}, find_one=True)
        if not body_type_data:
            raise ValueError(f"Body type not found for id: {body_types}")
        body_type = body_type_data['name']

        contract_type_data = admin_db.find(AdminDBColls.CONTRACT_TENURE, {"id": contract_types}, find_one=True)
        if not contract_type_data:
            raise ValueError(f"Contract type not found for id: {contract_types}")
        contract_type = contract_type_data['name']

        business_type_data = admin_db.find(AdminDBColls.BUSINESS_SEGMENTS, {"id": business_types}, find_one=True)
        if not business_type_data:
            raise ValueError(f"Business type not found for id: {business_types}")
        business_type = business_type_data['name']

        regex_freight_filter = {
            'from': {"$regex": from_city, "$options": "i"},
            'to': {"$regex": to_city, "$options": "i"},
            'BODY_TYPE': {"$regex": body_type, "$options": "i"},
            'Contract_type': {"$regex": contract_type, "$options": "i"},
            'BUSINESS_TYPE': {"$regex": business_type, "$options": "i"},
            'vehicle_capacity_number': {"$in": capacities if isinstance(capacities, list) else [capacities]}
        }
        historic_freight = db.find(FreightIndexDBColls.PREDICTED_FREIGHT, regex_freight_filter, find_one=True)
        return historic_freight['xg_boost_prediction']
    except ValueError as e:
        logger.info(f"ValueError in fetch_historic_freight: {str(e)}")
    except Exception as e:
        logger.info(f"Unexpected error in fetch_historic_freight: {str(e)}")
    return None
