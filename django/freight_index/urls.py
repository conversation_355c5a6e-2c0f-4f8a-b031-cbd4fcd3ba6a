from django.urls import path
from .views import (
    PredictAPI,
    DropdownsAPIView,
    LocationsAPIView,
)
from .exim_views import (
    EximShippingTypesApi,
    EximCalculateApi,
    EximLocationsApi,
    EximRegionsApi
)

urlpatterns = [
    path('predict/', PredictAPI.as_view(), name='predict'),
    path('dropdowns/', DropdownsAPIView.as_view(), name='dropdowns'),
    path('locations/', LocationsAPIView.as_view(), name='locations'),

    # EXIM - Sea Rates Urls
    path('exim/shipping-types/', EximShippingTypesApi.as_view(), name='exim_shipping_types'),
    path('exim/regions/', EximRegionsApi.as_view(), name='exim_regions'),
    path('exim/locations/', EximLocationsApi.as_view(), name='exim_locations'),
    path('exim/calculate/', EximCalculateApi.as_view(), name='exim_calculate')
]
