from pydantic import (
    PositiveInt,
    BaseModel
)


class EximCalculatePayloadValidator(BaseModel):
    index_id: str = ""
    shipping_type: PositiveInt
    transport_unit_type: PositiveInt | None
    bulk_size: int | None
    carrier: int | None
    origin_location_type: str = 'REGION'
    origin_location_id: PositiveInt
    destination_location_type: str = 'REGION'
    destination_location_id: PositiveInt
    date_from: str = ""
    date_to: str = ""
    group_by: str = "MONTH"
