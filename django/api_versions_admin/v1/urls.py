from django.urls import path, include
from .views import ClearCache


urlpatterns = [
    path('clear-cache/', ClearCache.as_view(), name='clear_cache'),
    path('master/', include(('masters.urls', 'masters'), namespace='masters')),
    path('ui-config/', include(('masters.ui_config.urls', 'ui_config'), namespace='ui_config')),
    path('profile/', include(('masters.profile_management.urls', 'profile_management'), namespace='profile_management')),
    path('user-management/', include(('masters.user_management.urls', 'user_management'), namespace='user_management')),
    path('subscriptions/', include(('masters.subscriptions.urls', 'subscriptions'), namespace='subscriptions')),
    path('plans/', include(('masters.plans.urls', 'plans'), namespace='plans')),
    path('settings/', include(('masters.settings.urls', 'settings'), namespace='settings')),
    path('utility/', include(('masters.utility.urls', 'utility'), namespace='utility')),
    path('carbon-emissions/', include(('carbon_emissions.urls', 'carbon_emissions'), namespace='carbon_emissions')),
    path('freight-index/', include(('freight_index.urls', 'freight_index'), namespace='freight_index')),
]
