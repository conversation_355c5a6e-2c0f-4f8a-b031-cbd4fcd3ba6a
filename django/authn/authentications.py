from rest_framework import authentication, exceptions, status
from django.urls import resolve
from django.conf import settings
from utils import (
    ErrorResponse, RedisUtils,
    DateUtil, get_client_details
)
from utils.constants import (
    ErrorMessages, AdminDBColls,
    CompanyType,
    UserRole,
    UserType
)
from utils.mongo import MongoUtility


def get_session_data(request, raise_error=False):
    request.is_token_valid = False
    request.is_seeker = False
    request.is_seeker_admin = False
    request.is_provider = False
    request.is_super_admin = False

    headers = request.headers
    user_agent = headers.get('user-agent', 'Unknown')
    token = headers.get('token', None)

    redis_inst = RedisUtils()
    session = {}
    if token:
        session = redis_inst.get_data(token)
        if not session:
            db = MongoUtility()
            session = db.find(AdminDBColls.USER_SESSIONS, {'token': token}, {'_id': 0, 'datetime': 0}, find_one=True)
            redis_inst.set_data(token, session)

    if not session:
        if raise_error:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.AUTHENTICATION_ERROR,
                    ErrorMessages.INVALID_TOKEN,
                    status.HTTP_401_UNAUTHORIZED
                )
            )
        return request

    request.user_agent = user_agent
    request.token = token
    request.now = DateUtil.get_current_timestamp()
    request.session_id = session.get('session_id')
    request.force_auth = session.get('force_auth', False)
    request.is_sso_login = session.get('is_sso_login', False)
    request.company_id = session.get('company_id')
    request.company_type = session.get('company_type')
    request.company_name = session.get('company_name')
    request.user_id = session.get('user_id')
    request.user_type = session.get('user_type')
    request.user_role = session.get('user_role')
    request.user_name = session.get('user_name', '')
    request.email = session.get('email')
    request.user_permissions = session.get('permissions', [])
    request.is_acc_active = session.get('is_acc_active', False)
    request.is_email_verified = session.get('is_email_verified', False)
    request.is_seeker = (request.user_type == UserType.SEEKER.value) and (request.company_type == CompanyType.SEEKER.value)
    request.is_seeker_admin = request.is_seeker and (request.user_role == UserRole.ADMIN_SEEKER.value)
    request.is_provider = (request.user_type == UserType.PROVIDER.value) and (request.company_type == CompanyType.PROVIDER.value)
    request.is_super_admin = (request.user_type == UserType.SCLEN.value) and (request.user_role == UserRole.SUPER_ADMIN.value)
    request.is_token_valid = True
    return request


class AuthenticateSuperAdmin(authentication.BaseAuthentication):
    """This authentication is used to only authorize seeker admins."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not request.is_super_admin:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateSeekerAdmin(authentication.BaseAuthentication):
    """This authentication is used to only authorize seeker admins."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker_admin):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateSeeker(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateProvider(authentication.BaseAuthentication):
    """This authentication is used to only authorize providers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_provider):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateAll(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers or providers. This does not authorize drivers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_super_admin or request.is_seeker or request.is_provider):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticatePostFirstAnonymousAccess(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers from the 2nd request onwards. First hit is anonymous."""

    AUTH_BYPASS_APIS = ["emission_compute", "freight_index_predict"]

    def authenticate(self, request):
        # Resolve the current path name
        match = resolve(request.path_info)
        path_name = match.url_name

        client_ip = get_client_details(request)['ip']
        redis_key = f"auth_bypass_ip:{client_ip}:{path_name}"
        request.rate_limit_key = redis_key

        redis_inst = RedisUtils(settings.DJANGO_CACHE_DB_URL)

        if not redis_inst.get_data(redis_key):
            # Allow unauthenticated access for the first time
            redis_inst.set_data(redis_key, "1", expiry=86400)  # 1 day expiry
            return (None, None)

        request = get_session_data(request, raise_error=False)
        if not request.is_token_valid:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.RATE_LIMIT_ERROR,
                    "You’ve reached the daily limit, sign in to get unlimited access. No payment required.",
                    status.HTTP_429_TOO_MANY_REQUESTS
                )
            )

        redis_inst.delete_data(redis_key)
        return ({}, None)
