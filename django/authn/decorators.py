import json
import hashlib
from functools import wraps
from pydantic import ValidationError
from rest_framework import status
from rest_framework.response import Response
from django.core.cache import cache
from django.conf import settings
from utils import format_error_response, RedisUtils


def cache_api_response(timeout=3600, is_generic=False):  # Default: Cache for 1 hour
    """Decorator to cache API responses including user context"""
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            user_id = getattr(request, "user_id", "anonymous")  # Handle logged-in & anonymous users

            # Build a cache key based on the full URL and user ID
            query_params = request.GET.dict()  # Convert QueryDict to a normal dict
            sorted_params = json.dumps(query_params, sort_keys=True)  # Sort params to maintain consistency

            if is_generic:
                cache_key = hashlib.md5(f"{request.path}?{sorted_params}".encode()).hexdigest()
            else:
                cache_key = hashlib.md5(f"{request.path}?{sorted_params}&user_id={user_id}".encode()).hexdigest()

            # Check if response is cached
            cached_response = cache.get(cache_key)
            if cached_response:
                return Response(cached_response)  # Return cached response if found

            # Call the actual view and cache the response
            response = view_func(request, *args, **kwargs)

            # Cache the response if it's a valid JSON response
            if response.status_code == 200:
                cache.set(cache_key, response.data, timeout=timeout)
            return response
        return _wrapped_view
    return decorator


def reset_limit_on_error_response():
    """Decorator to reset rate limit on error response."""
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Call the actual view
            response = view_func(request, *args, **kwargs)

            # Reset the limit if it's an error response
            if response.status_code in [400, 500]:
                redis_inst = RedisUtils(settings.DJANGO_CACHE_DB_URL)
                redis_inst.delete_data(request.request.rate_limit_key)
            return response
        return _wrapped_view
    return decorator


def validate_query_params(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                query_params = request.GET.dict()
                validated_data = validator_class(**query_params)
                request.validated_params = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def validate_request_payload(validator_class):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            try:
                payload = request.data
                validated_data = validator_class(**payload)
                request.validated_payload = validated_data
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator
