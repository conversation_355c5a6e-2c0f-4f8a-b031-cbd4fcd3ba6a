from django.urls import path
from .views import (
    BookDemoAPI,
    ListDemoBookingsAPI,
    CountDemoBookingsAPI,
    DemoBookingRetriveUpdateDeleteAPI
)

urlpatterns = [
    path('book-demo/', BookDemoAPI.as_view(), name='book_demo'),
    path('listing/', ListDemoBookingsAPI.as_view(), name='list_demo_bookings'),
    path('counts/', CountDemoBookingsAPI.as_view(), name='count_demo_bookings'),
    path('<str:booking_id>/', DemoBookingRetriveUpdateDeleteAPI.as_view(), name='demo_booking_crud'),
]
