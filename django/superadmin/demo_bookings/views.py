import logging
from datetime import datetime, date
from rest_framework import status
from rest_framework.views import APIView
from django.utils.decorators import method_decorator
from utils import (
    format_response,
    format_error_response,
    get_uuid
)
from utils.constants import AdminDBColls, DemoBookingStatus
from utils.mongo import MongoUtility
from utils.date_util import DateUtil
from authn.decorators import validate_request_payload
from authn import AuthenticateSuperAdmin
from .request_validators import DemoBookingPayloadValidator

logger = logging.getLogger('application')


@method_decorator(validate_request_payload(DemoBookingPayloadValidator), name='post')
class BookDemoAPI(APIView):
    """
    API for booking product demos.
    """

    def post(self, request):
        """
        Book a demo for selected modules.

        Request Body:
        {
            "first_name": "John",
            "last_name": "Doe",
            "company_name": "ABC Corp",
            "designation": "Manager",
            "email": "<EMAIL>",
            "phone": 9876543210,
            "product_ids": [1, 2],
            "demo_date": "2024-01-15",
            "message": "Interested in learning more about your platform"
        }
        """
        payload = request.validated_payload

        # Generate unique booking ID
        booking_id = get_uuid()
        current_timestamp = DateUtil.get_current_timestamp()

        # Prepare booking data
        booking_data = {
            'id': booking_id,
            'first_name': payload.first_name,
            'last_name': payload.last_name,
            'company_name': payload.company_name,
            'designation': payload.designation,
            'email': payload.email,
            'phone': payload.phone,
            'product_ids': payload.product_ids,
            'demo_date': payload.demo_date,
            'message': payload.message,
            'status': DemoBookingStatus.PENDING.name,
            'status_id': DemoBookingStatus.PENDING.value,
            'remarks': None,
            'created_on': current_timestamp,
            'updated_on': current_timestamp
        }

        # Save to database
        db = MongoUtility()
        db.insert(AdminDBColls.DEMO_BOOKINGS, [booking_data])

        # Prepare response
        booking_data.pop('_id', None)
        response_data = booking_data

        from masters.tasks import send_demo_booking_email
        send_demo_booking_email(booking_data)

        return format_response(status.HTTP_200_OK, response_data, 'Demo booked successfully')


class ListDemoBookingsAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        """
        Get all demo bookings (for admin use).

        Query Parameters:
        - status_id: Filter by booking status_id (pending, confirmed, completed, cancelled)
        - email: Filter by email address
        - company_name: Filter by company name
        - limit: Number of records to return (default: 20)
        - offset: Number of records to skip (default: 0)
        """
        try:
            booking_status_id = int(request.GET.get('status_id'))
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid status_id')

        search_term = request.GET.get('search_term')
        limit = int(request.GET.get('limit', 20))
        offset = int(request.GET.get('offset', 0))

        # Build query
        query = {'status_id': booking_status_id}
        if search_term:
            query['company_name'] = {'$regex': search_term, '$options': 'i'}

        # Get bookings from database
        db = MongoUtility()
        bookings = list(db.find(AdminDBColls.DEMO_BOOKINGS, query, sort=[('created_on', -1)]).limit(limit).skip(offset))

        # Get total count
        total_count = db.find(AdminDBColls.DEMO_BOOKINGS, query).count()

        response_data = {
            'bookings': bookings,
            'total_count': total_count,
            'limit': limit,
            'offset': offset
        }
        return format_response(status.HTTP_200_OK, response_data, 'Demo bookings retrieved successfully')


class CountDemoBookingsAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        """
        Get all demo bookings (for admin use).

        Query Parameters:
        - status_id: Filter by booking status_id (pending, confirmed, completed, cancelled)
        - email: Filter by email address
        - company_name: Filter by company name
        """
        self.db = MongoUtility()

        response_data = {
            'totals': self.get_total_counts(query={})
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def get_total_counts(self, query):
        query['status_id'] = {'$in': [x.value for x in DemoBookingStatus]}

        group_query = {
            "_id": "$status_id",
            "count": {"$sum": 1}
        }

        project_query = {
            "_id": 0,
            "status_id": "$_id",
            "count": 1
        }

        aggregate_result = list(self.db.aggregate(AdminDBColls.DEMO_BOOKINGS, query, group_query, project_query))
        return aggregate_result


class DemoBookingRetriveUpdateDeleteAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request, *args, **kwargs):
        booking_id = kwargs['booking_id']

        # Get bookings from database
        db = MongoUtility()

        booking_doc = db.find(AdminDBColls.DEMO_BOOKINGS, {'id': booking_id}, find_one=True)
        if not booking_doc:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Booking not found.')

        response_data = booking_doc
        return format_response(status.HTTP_200_OK, response_data, 'Demo booking retrieved successfully')

    def put(self, request, *args, **kwargs):
        """
        Update demo booking status.

        Request Body:
        {
            "status_id": 2,
            "demo_date": "2024-01-15",
            "remarks": "Demo confirmed for the requested date"
        }
        """
        booking_id = kwargs['booking_id']

        # Get update data
        update_data = request.data

        if update_data.get('status_id') not in DemoBookingStatus:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid status_id')

        # Add updated timestamp
        update_query = {
            'status_id': update_data['status_id'],
            'status': DemoBookingStatus(update_data['status_id']).name,
            'updated_on': request.now
        }

        if update_data.get('remarks'):
            update_query['remarks'] = update_data['remarks']

        if update_data.get('demo_date'):
            demo_date = update_data['demo_date']
            try:
                # Try to parse the date
                demo_date_obj = datetime.strptime(demo_date, '%Y-%m-%d').date()
            except ValueError:
                return format_error_response(status.HTTP_400_BAD_REQUEST, f'Demo date must be in YYYY-MM-DD format')

            # Check if the date is not in the past
            today = date.today()
            if demo_date_obj < today:
                return format_error_response(status.HTTP_400_BAD_REQUEST, f'Demo date cannot be in the past')

            update_query['demo_date'] = demo_date

        # Update booking in database
        db = MongoUtility()

        query = {'id': booking_id}

        # Check if booking exists
        existing_booking = db.find(AdminDBColls.DEMO_BOOKINGS, query, find_one=True)
        if not existing_booking:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Demo booking not found')

        # Update the booking
        db.update(AdminDBColls.DEMO_BOOKINGS, query, update_data)

        return format_response(status.HTTP_200_OK, {}, 'Demo booking updated successfully')

    def delete(self, request, *args, **kwargs):
        booking_id = kwargs['booking_id']

        # Get bookings from database
        db = MongoUtility()

        db.delete(AdminDBColls.DEMO_BOOKINGS, {'id': booking_id})
        return format_response(status.HTTP_200_OK, {}, 'Demo booking deleted successfully')
