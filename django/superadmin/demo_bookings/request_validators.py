from datetime import datetime, date
from pydantic import BaseModel, Field, EmailStr, field_validator
from utils.constants import SAASProduct
from utils import is_valid_domain


class DemoBookingPayloadValidator(BaseModel):
    """
    Validator for demo booking requests.
    """
    first_name: str = Field(..., min_length=1, max_length=30, description="First name of the person booking the demo")
    last_name: str = Field(..., min_length=1, max_length=30, description="Last name of the person booking the demo")
    company_name: str = Field(..., min_length=1, max_length=50, description="Company name")
    designation: str = Field(..., min_length=1, max_length=50, description="Designation/Role of the person")
    email: EmailStr = Field(..., description="Email address")
    phone: int = Field(..., description="Phone number")
    product_ids: list[int] = Field(..., min_items=1, description="List of selected product_ids")
    demo_date: str | None = Field(default=None, description="Preferred demo date (YYYY-MM-DD format)")
    message: str | None = Field(default=None, max_length=1000, description="Additional message")

    @field_validator('email', mode='after')
    @classmethod
    def validate_email(cls, value: str) -> str:
        if not is_valid_domain(value):
            raise ValueError("Invalid email domain.")
        return value.lower()

    @field_validator('phone', mode="before")
    @classmethod
    def validate_phone(cls, v):
        """Validate phone number format"""
        # Remove any non-digit characters
        phone_digits = ''.join(filter(str.isdigit, str(v)))

        # Check if it's a valid length
        if len(phone_digits) != 10:
            raise ValueError('Phone number must be valid 10 digits')

        return int(phone_digits)

    @field_validator('product_ids', mode="after")
    @classmethod
    def validate_product_ids(cls, v):
        """Validate that product_ids are from the allowed list"""
        for product_id in v:
            if product_id not in SAASProduct:
                raise ValueError(f'Invalid product_id - {product_id}')
        return v

    @field_validator('demo_date', mode="after")
    @classmethod
    def validate_demo_date(cls, v):
        """Validate demo date format and ensure it's not in the past"""
        if v is None:
            return v

        try:
            # Try to parse the date
            demo_date = datetime.strptime(v, '%Y-%m-%d').date()
        except ValueError:
            raise ValueError('Demo date must be in YYYY-MM-DD format')

        # Check if the date is not in the past
        today = date.today()
        if demo_date < today:
            raise ValueError('Demo date cannot be in the past')

        return v
