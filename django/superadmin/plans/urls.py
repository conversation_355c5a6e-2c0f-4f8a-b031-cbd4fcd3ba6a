from django.urls import path
from .views import (
    ListFeatureGroupsAPI,
    FeatureGroupCRUDAPI,
    ListFeaturesAPI,
    FeatureCRUDAPI,
    BulkFeatureUploadAPI,
    FeatureTemplateDownloadAPI
)

urlpatterns = [
    # Feature Groups URLs
    path('products/<int:product_id>/feature-groups/', ListFeatureGroupsAPI.as_view(), name='list_feature_groups'),
    path('products/<int:product_id>/feature-groups/create/', FeatureGroupCRUDAPI.as_view(), name='create_feature_group'),
    path('products/<int:product_id>/feature-groups/<str:group_id>/', FeatureGroupCRUDAPI.as_view(), name='feature_group_detail'),

    # Features URLs
    path('products/<int:product_id>/features/', ListFeaturesAPI.as_view(), name='list_features'),
    path('products/<int:product_id>/features/create/', FeatureCRUDAPI.as_view(), name='create_feature'),
    path('products/<int:product_id>/features/bulk-upload/', BulkFeatureUploadAPI.as_view(), name='bulk_feature_upload'),
    path('products/<int:product_id>/features/template/', FeatureTemplateDownloadAPI.as_view(), name='feature_template_download'),
    path('products/<int:product_id>/features/<str:feature_id>/', FeatureCRUDAPI.as_view(), name='feature_detail'),
]