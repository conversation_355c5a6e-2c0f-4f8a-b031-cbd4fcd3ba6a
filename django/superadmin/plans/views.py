import logging
import pandas as pd
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser
from django.utils.decorators import method_decorator
from pydantic import ValidationError
from utils import (
    format_response,
    format_error_response,
    generate_hash_id,
    DateUtil,
)
from utils.constants import AdminDBColls, SAASProduct, DataType, ValueType
from utils.mongo import MongoUtility
from authn.decorators import validate_request_payload, validate_query_params
from authn import AuthenticateSuperAdmin
from schema import FeatureGroupSchema, FeatureSchema
from .request_validators import (
    ListFeatureGroupsParamsValidator,
    ListFeaturesParamsValidator,
    CreateFeatureGroupValidator,
    UpdateFeatureGroupValidator,
    CreateFeatureValidator,
    UpdateFeatureValidator
)

logger = logging.getLogger('application')


# Feature Groups CRUD APIs
@method_decorator(validate_query_params(ListFeatureGroupsParamsValidator), name='get')
class ListFeatureGroupsAPI(APIView):
    """API to list feature groups with filtering and pagination"""
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request, product_id):
        """
        Get all feature groups for a specific product with optional filtering.

        URL Parameters:
        - product_id: Product ID from URL path

        Query Parameters:
        - search_term: Search in group name
        - limit: Number of records to return (default: 20)
        - offset: Number of records to skip (default: 0)
        """
        params = request.validated_params

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        # Build query with product_id from URL
        query = {'product_id': product_id}
        if params.search_term:
            query['name'] = {'$regex': params.search_term, '$options': 'i'}

        # Get feature groups from database
        db = MongoUtility()
        feature_groups = db.find(AdminDBColls.FEATURE_GROUPS, query, sort=[('created_on', -1)]).limit(params.limit).skip(params.offset)

        # Get total count
        total_count = db.find(AdminDBColls.FEATURE_GROUPS, query).count()

        response_data = {
            'feature_groups': list(feature_groups),
            'total_count': total_count,
            'limit': params.limit,
            'offset': params.offset,
            'product_id': product_id
        }

        return format_response(status.HTTP_200_OK, response_data, 'Feature groups retrieved successfully')


@method_decorator(validate_request_payload(CreateFeatureGroupValidator), name='post')
@method_decorator(validate_request_payload(UpdateFeatureGroupValidator), name='put')
class FeatureGroupCRUDAPI(APIView):
    """API for creating, retrieving, updating and deleting feature groups"""
    authentication_classes = (AuthenticateSuperAdmin, )

    def post(self, request, product_id):
        """
        Create a new feature group for a specific product.

        URL Parameters:
        - product_id: Product ID from URL path

        Request Body:
        {
            "name": "Key Features",
        }
        """
        payload = request.validated_payload

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        try:
            # Add product_id from URL to payload
            payload_data = payload.model_dump()
            payload_data['product_id'] = product_id

            # Create feature group using schema
            feature_group_data = FeatureGroupSchema(**payload_data).model_dump()

            # Check if feature group with same ID already exists
            db = MongoUtility()
            fid = feature_group_data['id']
            query = {'id': fid, 'product_id': product_id}
            existing_group = db.find(AdminDBColls.FEATURE_GROUPS, query, find_one=True)

            if existing_group:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f"Feature group with ID '{fid}' already exists."
                )

            # Save to database
            db.insert(AdminDBColls.FEATURE_GROUPS, [feature_group_data])

            # Remove MongoDB _id from response
            feature_group_data.pop('_id', None)

            return format_response(status.HTTP_200_OK, feature_group_data, 'Feature group created successfully')
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
        except Exception as e:
            logger.error(f"Error creating feature group: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'Failed to create feature group. Please try again.'
            )

    def get(self, request, product_id, group_id):
        """Get a specific feature group by ID for a specific product"""
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        db = MongoUtility()

        feature_group = db.find(AdminDBColls.FEATURE_GROUPS, {'id': group_id, 'product_id': product_id}, find_one=True)

        if not feature_group:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Feature group not found')

        return format_response(status.HTTP_200_OK, feature_group, 'Feature group retrieved successfully')

    def put(self, request, product_id, group_id):
        """
        Update a feature group for a specific product.

        URL Parameters:
        - product_id: Product ID from URL path
        - group_id: Feature group ID from URL path

        Request Body:
        {
            "name": "Updated Key Features",
        }
        """
        payload = request.validated_payload

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        try:
            db = MongoUtility()
            query = {'id': group_id, 'product_id': product_id}

            # Check if feature group exists
            existing_group = db.find(AdminDBColls.FEATURE_GROUPS, query, None, find_one=True)
            if not existing_group:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    'Feature group not found'
                )

            # Prepare update data
            update_data = payload.model_dump()
            update_data['product_id'] = product_id  # Ensure product_id from URL
            update_data['updated_on'] = DateUtil.get_current_timestamp()

            # If name or product changed, regenerate ID
            is_name_changed = update_data.get('name') != existing_group.get('name')
            if is_name_changed:
                new_id = generate_hash_id(f'{product_id}{update_data["name"]}')
                update_data['id'] = new_id

                # Check if new ID conflicts with existing groups
                existing_with_new_id = db.find(
                    AdminDBColls.FEATURE_GROUPS,
                    {'id': new_id, '_id': {'$ne': existing_group['_id']}},
                    find_one=True
                )
                if existing_with_new_id:
                    return format_error_response(
                        status.HTTP_400_BAD_REQUEST,
                        f"Feature group with ID '{new_id}' already exists."
                    )

            # Update the feature group
            db.update(AdminDBColls.FEATURE_GROUPS, query, update_data)

            return format_response(status.HTTP_200_OK, {}, 'Feature group updated successfully')
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
        except Exception as e:
            logger.error(f"Error updating feature group {group_id}: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'Failed to update feature group. Please try again.'
            )

    def delete(self, request, product_id, group_id):
        """Delete a feature group for a specific product"""
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        db = MongoUtility()

        # Check if feature group exists
        existing_group = db.find(AdminDBColls.FEATURE_GROUPS, {'id': group_id, 'product_id': product_id}, find_one=True)

        if not existing_group:
            return format_error_response(
                status.HTTP_404_NOT_FOUND,
                'Feature group not found'
            )

        # Check if there are features using this group
        features_using_group = db.find(AdminDBColls.FEATURES, {'gid': group_id}, find_one=True)

        if features_using_group:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Cannot delete feature group. Features are still using this group.'
            )

        # Delete the feature group
        db.delete(AdminDBColls.FEATURE_GROUPS, {'id': group_id, 'product_id': product_id})

        logger.info(f"Feature group {group_id} deleted successfully")

        return format_response(
            status.HTTP_200_OK,
            {},
            'Feature group deleted successfully'
        )


# Features CRUD APIs
@method_decorator(validate_query_params(ListFeaturesParamsValidator), name='get')
class ListFeaturesAPI(APIView):
    """API to list features with filtering and pagination"""
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request, product_id):
        """
        Get all features for a specific product with optional filtering.

        URL Parameters:
        - product_id: Product ID from URL path

        Query Parameters:
        - group_id: Filter by feature group ID
        - search_term: Search in feature name
        - is_active: Filter by active status
        - is_addon: Filter by addon status
        - limit: Number of records to return (default: 20)
        - offset: Number of records to skip (default: 0)
        """
        params = request.validated_params

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        # Build query with product_id from URL
        query = {'product_id': product_id}
        if params.group_id:
            query['gid'] = params.group_id
        if params.search_term:
            query['name'] = {'$regex': params.search_term, '$options': 'i'}
        if params.is_active is not None:
            query['is_active'] = params.is_active
        if params.is_addon is not None:
            query['is_addon'] = params.is_addon

        params.limit = 300  # temp until ui implements pagination
        # Get features from database
        db = MongoUtility()
        features = db.find(AdminDBColls.FEATURES, query, sort=[('created_on', -1)]).limit(params.limit).skip(params.offset)

        # Get total count
        total_count = features.count()

        response_data = {
            'features': list(features),
            'total_count': total_count,
            'limit': params.limit,
            'offset': params.offset,
            'product_id': product_id
        }

        return format_response(
            status.HTTP_200_OK,
            response_data,
            'Features retrieved successfully'
        )


@method_decorator(validate_request_payload(CreateFeatureValidator), name='post')
@method_decorator(validate_request_payload(UpdateFeatureValidator), name='put')
class FeatureCRUDAPI(APIView):
    """API for creating, retrieving, updating and deleting features"""
    authentication_classes = (AuthenticateSuperAdmin, )

    def post(self, request, product_id):
        """
        Create a new feature for a specific product.

        URL Parameters:
        - product_id: Product ID from URL path

        Request Body:
        {
            "name": "Lanes Included",
            "gid": "pro_key_features",
            "gname": "Key Features",
            "options": [
                {
                    "ilabel": "50",
                    "idata_type": "int",
                    "ival_type": "number",
                    "ival": 50
                }
            ],
            "is_active": true,
            "is_kf": true,
            "is_cfg": false,
            "has_bl": true,
            "track_usage": true,
            "apply_disc": false,
            "is_addon": false,
            "addon_toggle": false
        }
        """
        payload = request.validated_payload

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        try:
            # Add product_id from URL to payload
            payload_data = payload.model_dump()
            payload_data['product_id'] = product_id

            # Validate that the feature group exists and belongs to the same product
            db = MongoUtility()
            group_exists = db.find(AdminDBColls.FEATURE_GROUPS, {'id': payload.gid, 'product_id': product_id}, find_one=True)

            if not group_exists:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f'Feature group with ID "{payload.gid}" does not exist for product {product_id}'
                )

            # Create feature using schema
            feature_data = FeatureSchema(**payload_data).model_dump()

            # Check if feature with same ID already exists
            fid = feature_data['id']
            query = {'id': fid, 'product_id': product_id}
            existing_feature = db.find(AdminDBColls.FEATURES, query, find_one=True)

            if existing_feature:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f"Feature with ID '{fid}' already exists."
                )

            # Save to database
            db.insert(AdminDBColls.FEATURES, [feature_data])

            # Remove MongoDB _id from response
            feature_data.pop('_id', None)

            return format_response(status.HTTP_200_OK, feature_data, 'Feature created successfully')
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
        except Exception as e:
            logger.error(f"Error creating feature: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'Failed to create feature. Please try again.'
            )

    def get(self, request, product_id, feature_id):
        """Get a specific feature by ID for a specific product"""
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        db = MongoUtility()

        feature = db.find(
            AdminDBColls.FEATURES,
            {'id': feature_id, 'product_id': product_id},
            {'_id': 0},
            find_one=True
        )

        if not feature:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Feature not found')

        return format_response(status.HTTP_200_OK, feature, 'Feature retrieved successfully')

    def put(self, request, product_id, feature_id):
        """
        Update a feature for a specific product.

        URL Parameters:
        - product_id: Product ID from URL path
        - feature_id: Feature ID from URL path

        Request Body:
        {
            "name": "Updated Lanes Included",
            "gid": "pro_key_features",
            "gname": "Key Features",
            "sgid": "pro_sub_key_features",
            "sgname": "Sub Key Features",
            "options": [
                {
                    "ilabel": "100",
                    "idata_type": "int",
                    "ival_type": "number",
                    "ival": 100
                }
            ],
            "is_active": true,
            "is_kf": true,
            "is_cfg": false,
            "has_bl": true,
            "track_usage": true,
            "apply_disc": false,
            "is_addon": false,
            "addon_toggle": false
        }
        """
        payload = request.validated_payload

        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        try:
            db = MongoUtility()
            query = {'id': feature_id, 'product_id': product_id}

            # Check if feature exists
            existing_feature = db.find(AdminDBColls.FEATURES, query, None, find_one=True)
            if not existing_feature:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    'Feature not found'
                )

            # Validate that the feature group exists and belongs to the same product
            group_exists = db.find(
                AdminDBColls.FEATURE_GROUPS,
                {'id': payload.gid, 'product_id': product_id},
                find_one=True
            )

            if not group_exists:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f'Feature group with ID "{payload.gid}" does not exist for product {product_id}'
                )

            # Prepare update data
            update_data = payload.model_dump()
            update_data['product_id'] = product_id  # Ensure product_id from URL
            update_data['updated_on'] = DateUtil.get_current_timestamp()

            # If name or group changed, regenerate ID
            is_name_changed = update_data.get('name') != existing_feature.get('name')
            is_gid_changed = update_data.get('gid') != existing_feature.get('gid')
            is_sgid_changed = update_data.get('sgid') != existing_feature.get('sgid')
            if is_name_changed or is_gid_changed or is_sgid_changed:
                new_id = generate_hash_id(f"{product_id}{update_data['gname']}{update_data.get('sgname') or ''}{update_data['name']}")
                update_data['id'] = new_id

                # Check if new ID conflicts with existing features
                existing_with_new_id = db.find(
                    AdminDBColls.FEATURES,
                    {'id': new_id, '_id': {'$ne': existing_feature['_id']}},
                    find_one=True
                )
                if existing_with_new_id:
                    return format_error_response(
                        status.HTTP_400_BAD_REQUEST,
                        f"Feature with ID '{new_id}' already exists."
                    )

            # Update the feature
            db.update(AdminDBColls.FEATURES, query, update_data)

            return format_response(status.HTTP_200_OK, {}, 'Feature updated successfully')
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
        except Exception as e:
            logger.error(f"Error updating feature {feature_id}: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'Failed to update feature. Please try again.'
            )

    def delete(self, request, product_id, feature_id):
        """Delete a feature for a specific product"""
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        db = MongoUtility()

        # Check if feature exists
        existing_feature = db.find(AdminDBColls.FEATURES, {'id': feature_id, 'product_id': product_id}, find_one=True)

        if not existing_feature:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Feature not found')

        # Check if there are plans using this feature
        plans_using_feature = db.find(AdminDBColls.PLANS, {'features.id': feature_id}, find_one=True)

        if plans_using_feature:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Cannot delete feature. Plans are still using this feature.'
            )

        # Delete the feature
        db.delete(AdminDBColls.FEATURES, {'id': feature_id, 'product_id': product_id})

        logger.info(f"Feature {feature_id} deleted successfully")

        return format_response(status.HTTP_200_OK, {}, 'Feature deleted successfully')


class BulkFeatureUploadAPI(APIView):
    """API for bulk feature creation via Excel upload"""
    authentication_classes = (AuthenticateSuperAdmin, )
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request, product_id):
        """
        Create multiple features for a specific product via Excel upload.

        URL Parameters:
        - product_id: Product ID from URL path

        Request Body (multipart/form-data):
        - file: Excel file (.xlsx or .xls) containing feature data

        Excel File Format:
        The Excel file should have the following columns:
        - name: Feature name (required)
        - gname: Group name (required)
        - sgname: Sub group name (optional)
        - is_kf: Boolean (default: false)
        - is_cfg: Boolean (default: false)
        - has_bl: Boolean (default: false)
        - track_usage: Boolean (default: false)
        - option_1_label: First option label (required)
        - option_1_data_type: First option data type (str, int, bool)
        - option_1_value_type: First option value type (text, number, toggle, price)
        - option_2_label: Second option label (optional)
        - option_2_data_type: Second option data type (optional)
        - option_2_value_type: Second option value type (optional)
        - option_3_label: Third option label (optional)
        - option_3_data_type: Third option data type (optional)
        - option_3_value_type: Third option value type (optional)
        - option_4_label: Fourth option label (optional)
        - option_4_data_type: Fourth option data type (optional)
        - option_4_value_type: Fourth option value type (optional)

        Note: Option values are automatically derived from labels based on data type
        """
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        # Check if file is provided
        if 'file' not in request.FILES:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Excel file is required')

        excel_file = request.FILES['file']

        # Validate file extension
        if not excel_file.name.endswith(('.xlsx', '.xls')):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid file format. Please upload an Excel file (.xlsx or .xls)'
            )

        try:
            # Read Excel file
            df = pd.read_excel(excel_file)

            # Validate required columns (now using gname instead of gid)
            required_columns = ['name', 'gname']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f'Missing required columns: {", ".join(missing_columns)}'
                )

            # Process features
            self.db = MongoUtility()
            created_features = []
            errors = []

            groups = {x['name']: x for x in self.db.find(AdminDBColls.FEATURE_GROUPS, {'product_id': product_id})}
            existing_features = self.db.distinct(AdminDBColls.FEATURES, 'id', {'product_id': product_id})

            for index, row in df.iterrows():
                try:
                    feature_data = self._process_feature_row(row, product_id, groups, existing_features)
                    if feature_data:
                        created_features.append(feature_data)
                except Exception as e:
                    errors.append(f"Row {index + 2}: {str(e)}")

            # Insert valid features into database
            if created_features:
                self.db.insert(AdminDBColls.FEATURES, created_features, insert_many=True)

            # Prepare response
            response_data = {
                'total_rows': len(df),
                'created_features': len(created_features),
                'errors': errors,
                'product_id': product_id
            }

            if errors:
                return format_response(
                    status.HTTP_200_OK,
                    response_data,
                    f'Bulk upload completed with {len(errors)} errors'
                )
            else:
                return format_response(
                    status.HTTP_200_OK,
                    response_data,
                    'All features created successfully'
                )

        except Exception as e:
            logger.error(f"Error processing Excel file: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                f'Failed to process Excel file: {str(e)}'
            )

    def _process_feature_row(self, row, product_id, groups, existing_features):
        """
        Process a single row from the Excel file and create feature data.

        Args:
            row: Pandas Series representing a row from the Excel file
            product_id: Product ID
            db: Database connection

        Returns:
            dict: Feature data ready for insertion, or None if invalid

        Raises:
            Exception: If there's an error processing the row
        """
        # Extract basic feature data
        name = str(row.get('name', '')).strip()
        gname = str(row.get('gname', '')).strip()

        if not all([name, gname]):
            raise ValueError("Missing required fields: name or gname")

        # Find gid by looking up gname in feature groups
        group = groups.get(gname)
        if not group:
            raise ValueError(f"Feature group with name '{gname}' does not exist for product {product_id}")
        gid = group['id']

        # Extract optional sub-group fields
        sgname = str(row.get('sgname', '')).strip() if pd.notna(row.get('sgname')) else None
        sgid = None

        # If sgname is provided, derive sgid from sgname
        if sgname:
            # Use the already fetched group to get sub-groups
            for sub_group in group.get('sub_groups') or []:
                if sub_group['name'] == sgname:
                    sgid = sub_group['id']
                    break

            if not sgid:
                raise ValueError(f"Sub-group with name '{sgname}' does not exist in group '{gname}'")

        # Convert boolean fields (only the ones we allow in Excel)
        is_kf = self._convert_to_bool(row.get('is_kf', False))
        is_cfg = self._convert_to_bool(row.get('is_cfg', False))
        has_bl = self._convert_to_bool(row.get('has_bl', False))
        track_usage = self._convert_to_bool(row.get('track_usage', False))

        # Process options (up to 4 options)
        options = []
        for i in range(1, 5):  # option_1 to option_4
            option_label = row.get(f'option_{i}_label')
            option_data_type = row.get(f'option_{i}_data_type')
            option_value_type = row.get(f'option_{i}_value_type')

            if pd.notna(option_label) and str(option_label).strip():
                option_label = str(option_label).strip()

                if not all([pd.notna(x) and str(x).strip() for x in [option_data_type, option_value_type]]):
                    raise ValueError(f"Incomplete option {i}: label, data_type, and value_type are required")

                option_data_type = str(option_data_type).strip()
                option_value_type = str(option_value_type).strip()

                # Validate data type
                if option_data_type not in [dt.value for dt in DataType]:
                    raise ValueError(f"Invalid data_type for option {i}: {option_data_type}. Valid values: {[dt.value for dt in DataType]}")

                # Validate value type
                if option_value_type not in [vt.value for vt in ValueType]:
                    raise ValueError(f"Invalid value_type for option {i}: {option_value_type}. Valid values: {[vt.value for vt in ValueType]}")

                # Derive value from label based on data type
                try:
                    converted_value = self._derive_value_from_label(option_label, option_data_type)
                except ValueError as e:
                    raise ValueError(f"Option {i}: {str(e)}")

                option = {
                    'ilabel': option_label,
                    'idata_type': option_data_type,
                    'ival_type': option_value_type,
                    'ival': converted_value
                }
                options.append(option)

        if not options:
            raise ValueError("At least one option is required")

        # Prepare feature data
        feature_data = {
            'name': name,
            'gid': gid,
            'gname': gname,
            'sgid': sgid,
            'sgname': sgname,
            'product_id': product_id,
            'options': options,
            'is_kf': is_kf,
            'is_cfg': is_cfg,
            'has_bl': has_bl,
            'track_usage': track_usage,
        }

        # Create feature using schema for validation and ID generation
        try:
            feature_schema = FeatureSchema(**feature_data)
            validated_feature_data = feature_schema.model_dump()
            fid = validated_feature_data['id']
            # Check if feature with same ID already exists
            if fid in existing_features:
                raise ValueError(f"Feature with ID '{fid}' already exists.")
            else:
                existing_features.append(fid)

            return validated_feature_data
        except ValidationError as e:
            raise ValueError(f"Validation error: {str(e)}")

    def _convert_to_bool(self, value):
        """Convert various representations to boolean"""
        if pd.isna(value):
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ['true', 'yes', '1', 'on']
        if isinstance(value, (int, float)):
            return bool(value)
        return False

    def _derive_value_from_label(self, label, data_type):
        """
        Derive option value from label based on data type.

        Args:
            label: The option label (e.g., "50", "INR 150/-", "Yes", "Unlimited")
            data_type: The data type (str, int, bool)

        Returns:
            Converted value based on data type

        Raises:
            ValueError: If conversion fails
        """
        if not label or pd.isna(label):
            raise ValueError("Option label cannot be empty")

        label = str(label).strip()

        if data_type == DataType.STR.value:
            return label
        elif data_type == DataType.INT.value:
            # Extract numeric value from label
            # Handle cases like "50", "INR 150/-", "200 units", etc.
            import re

            # Try to extract number from the label
            numbers = re.findall(r'-?\d+(?:\.\d+)?', label)
            if not numbers:
                raise ValueError(f"Cannot extract numeric value from label '{label}' for int data type")

            try:
                # Use the first number found
                return int(float(numbers[0]))
            except (ValueError, TypeError):
                raise ValueError(f"Cannot convert '{numbers[0]}' from label '{label}' to integer")

        elif data_type == DataType.BOOL.value:
            # Convert common boolean representations
            label_lower = label.lower()
            if label_lower in ['true', 'yes', 'y', '1', 'on', 'enabled', 'active']:
                return True
            elif label_lower in ['false', 'no', 'n', '0', 'off', 'disabled', 'inactive']:
                return False
            else:
                raise ValueError(f"Cannot convert label '{label}' to boolean. Use: true/false, yes/no, 1/0, on/off, enabled/disabled, active/inactive")
        else:
            raise ValueError(f"Unsupported data type: {data_type}")


class FeatureTemplateDownloadAPI(APIView):
    """API to download Excel template for bulk feature upload"""
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request, product_id):
        """
        Download Excel template for bulk feature upload.

        URL Parameters:
        - product_id: Product ID from URL path
        """
        # Validate product_id exists in SAASProduct enum
        if product_id not in SAASProduct:
            return format_error_response(status.HTTP_400_BAD_REQUEST, f'Invalid product_id: {product_id}')

        try:
            # Fetch feature groups from database for the given product
            db = MongoUtility()
            feature_groups = list(db.find(AdminDBColls.FEATURE_GROUPS, {'product_id': product_id}))

            if not feature_groups:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    f'No feature groups found for product_id: {product_id}'
                )

            # Extract group names and sub-group names for dropdowns
            group_names = []
            sub_group_names = ['']  # Start with empty option for optional sub-groups

            for group in feature_groups:
                group_name = group['name']
                group_names.append(group_name)

                # Extract sub-groups if they exist
                if 'sub_groups' in group and group['sub_groups']:
                    for sub_group in group['sub_groups']:
                        sub_group_name = sub_group['name']
                        if sub_group_name not in sub_group_names:
                            sub_group_names.append(sub_group_name)

            # Create empty template structure (headers only, no sample data)
            sample_data = {
                'gname': [],
                'sgname': [],
                'name': [],
                'is_kf': [],
                'is_cfg': [],
                'has_bl': [],
                'track_usage': [],
                'option_1_label': [],
                'option_1_data_type': [],
                'option_1_value_type': [],
                'option_2_label': [],
                'option_2_data_type': [],
                'option_2_value_type': [],
                'option_3_label': [],
                'option_3_data_type': [],
                'option_3_value_type': [],
                'option_4_label': [],
                'option_4_data_type': [],
                'option_4_value_type': []
            }

            # Create DataFrame
            df = pd.DataFrame(sample_data)

            # Create Excel file in memory
            from io import BytesIO
            from django.http import HttpResponse

            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # Write sample data
                df.to_excel(writer, sheet_name='Features', index=False)

                # Get the worksheet to add data validation
                worksheet = writer.sheets['Features']

                # Import openpyxl for data validation
                from openpyxl.worksheet.datavalidation import DataValidation
                from openpyxl.utils import get_column_letter

                # Get enum values for dropdowns
                data_type_values = [dt.value for dt in DataType]
                value_type_values = [vt.value for vt in ValueType]
                boolean_values = ['true', 'false']

                # Create data validation for boolean columns
                bool_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(boolean_values)}"',
                    allow_blank=True
                )
                bool_validation.error = "Please select from the dropdown list"
                bool_validation.errorTitle = "Invalid Input"

                # Create data validation for group names
                group_name_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(group_names)}"',
                    allow_blank=False
                )
                group_name_validation.error = "Please select from the dropdown list: " + ", ".join(group_names)
                group_name_validation.errorTitle = "Invalid Group Name"

                # Create data validation for sub-group names
                sub_group_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(sub_group_names)}"',
                    allow_blank=True
                )
                sub_group_validation.error = "Please select from the dropdown list: " + ", ".join(sub_group_names)
                sub_group_validation.errorTitle = "Invalid Sub-Group Name"

                # Create data validation for data type columns
                data_type_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(data_type_values)}"',
                    allow_blank=True
                )
                data_type_validation.error = "Please select from the dropdown list: " + ", ".join(data_type_values)
                data_type_validation.errorTitle = "Invalid Data Type"

                # Create data validation for value type columns
                value_type_validation = DataValidation(
                    type="list",
                    formula1=f'"{",".join(value_type_values)}"',
                    allow_blank=True
                )
                value_type_validation.error = "Please select from the dropdown list: " + ", ".join(value_type_values)
                value_type_validation.errorTitle = "Invalid Value Type"

                # Add validations to worksheet
                worksheet.add_data_validation(bool_validation)
                worksheet.add_data_validation(group_name_validation)
                worksheet.add_data_validation(sub_group_validation)
                worksheet.add_data_validation(data_type_validation)
                worksheet.add_data_validation(value_type_validation)

                # Apply validation to columns (set range for 1000 rows since no sample data)
                max_row = 1000

                # Find column indices and apply boolean validation
                bool_columns = ['is_kf', 'is_cfg', 'has_bl', 'track_usage']
                for col_name in bool_columns:
                    if col_name in df.columns:
                        col_idx = df.columns.get_loc(col_name) + 1  # Excel is 1-indexed
                        col_letter = get_column_letter(col_idx)
                        bool_validation.add(f'{col_letter}2:{col_letter}{max_row}')

                # Apply group name validation
                if 'gname' in df.columns:
                    col_idx = df.columns.get_loc('gname') + 1
                    col_letter = get_column_letter(col_idx)
                    group_name_validation.add(f'{col_letter}2:{col_letter}{max_row}')

                # Apply sub-group name validation
                if 'sgname' in df.columns:
                    col_idx = df.columns.get_loc('sgname') + 1
                    col_letter = get_column_letter(col_idx)
                    sub_group_validation.add(f'{col_letter}2:{col_letter}{max_row}')

                # Apply data type validation to data type columns
                data_type_columns = ['option_1_data_type', 'option_2_data_type', 'option_3_data_type', 'option_4_data_type']
                for col_name in data_type_columns:
                    if col_name in df.columns:
                        col_idx = df.columns.get_loc(col_name) + 1
                        col_letter = get_column_letter(col_idx)
                        data_type_validation.add(f'{col_letter}2:{col_letter}{max_row}')

                # Apply value type validation to value type columns
                value_type_columns = ['option_1_value_type', 'option_2_value_type', 'option_3_value_type', 'option_4_value_type']
                for col_name in value_type_columns:
                    if col_name in df.columns:
                        col_idx = df.columns.get_loc(col_name) + 1
                        col_letter = get_column_letter(col_idx)
                        value_type_validation.add(f'{col_letter}2:{col_letter}{max_row}')

                # Add instructions sheet
                instructions = pd.DataFrame({
                    'Field': [
                        'gname', 'sgname', 'name', 'is_kf', 'is_cfg',
                        'has_bl', 'track_usage',
                        'option_X_label', 'option_X_data_type', 'option_X_value_type'
                    ],
                    'Description': [
                        'Group name (required dropdown)',
                        'Sub group name (optional dropdown)',
                        'Feature name (required)',
                        'Is key feature (dropdown: true/false)',
                        'Is configurable (dropdown: true/false)',
                        'Has business logic (dropdown: true/false)',
                        'Track usage (dropdown: true/false)',
                        'Option label (at least option_1 required)',
                        'Data type (dropdown: str, int, bool)',
                        'Value type (dropdown: text, number, toggle, price)'
                    ],
                    'Example': [
                        'Select from dropdown',
                        'Select from dropdown (optional)',
                        'Feature Name Example',
                        'true',
                        'false',
                        'true',
                        'true',
                        '50 or INR 150/- or Yes',
                        'int',
                        'number'
                    ],
                    'Valid_Values': [
                        ', '.join(group_names[:3]) + '... (dropdown)',
                        ', '.join([sg for sg in sub_group_names[:3] if sg]) + '... (dropdown)',
                        '',
                        'true, false (dropdown)',
                        'true, false (dropdown)',
                        'true, false (dropdown)',
                        'true, false (dropdown)',
                        'Any text (value auto-derived)',
                        'str, int, bool (dropdown)',
                        'text, number, toggle, price (dropdown)'
                    ],
                    'Notes': [
                        'Select from existing groups for this product',
                        'Select from existing sub-groups (optional)',
                        'Must be unique within the group',
                        'Use dropdown - other values will be rejected',
                        'Use dropdown - other values will be rejected',
                        'Use dropdown - other values will be rejected',
                        'Use dropdown - other values will be rejected',
                        'Value is auto-derived from label based on data_type',
                        'Use dropdown - other values will be rejected',
                        'Use dropdown - other values will be rejected'
                    ]
                })

                # Add enum values sheet for reference
                max_enum_length = max(len(data_type_values), len(value_type_values), len(boolean_values), 5)

                enum_values = pd.DataFrame({
                    'DataType_Values': data_type_values + [''] * (max_enum_length - len(data_type_values)),
                    'ValueType_Values': value_type_values + [''] * (max_enum_length - len(value_type_values)),
                    'Boolean_Values': boolean_values + [''] * (max_enum_length - len(boolean_values)),
                    'Label_Examples': [
                        '50 (for int data type)',
                        'INR 150/- (for int data type)',
                        'Unlimited (for str data type)',
                        'Yes (for bool data type)',
                        'Mon-Fri 9AM-5PM (for str data type)'
                    ] + [''] * (max_enum_length - 5),
                    'Notes': [
                        'Use these exact values in dropdowns',
                        'Values are case-sensitive',
                        'No custom values allowed',
                        'Option values auto-derived from labels',
                        'Empty cells are allowed for optional fields'
                    ] + [''] * (max_enum_length - 5)
                })
                instructions.to_excel(writer, sheet_name='Instructions', index=False)
                enum_values.to_excel(writer, sheet_name='Enum_Values', index=False)

            output.seek(0)

            # Create HTTP response
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="feature_upload_template_product_{product_id}.xlsx"'

            return response

        except Exception as e:
            logger.error(f"Error creating template: {str(e)}")
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                f'Failed to create template: {str(e)}'
            )
