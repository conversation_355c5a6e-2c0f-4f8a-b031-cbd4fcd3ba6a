from pydantic import BaseModel, Field
from schema import SubGroupSchema


# Feature Group Validators
class ListFeatureGroupsParamsValidator(BaseModel):
    """Validator for listing feature groups query parameters"""
    search_term: str | None = Field(default=None, max_length=100, description="Search in group name")
    limit: int = Field(default=20, ge=1, le=100, description="Number of records to return")
    offset: int = Field(default=0, ge=0, description="Number of records to skip")


class CreateFeatureGroupValidator(BaseModel):
    """Validator for creating feature groups"""
    name: str = Field(..., min_length=1, max_length=100, description="Group name")
    sub_groups: list[SubGroupSchema] | None = None


class UpdateFeatureGroupValidator(BaseModel):
    """Validator for updating feature groups"""
    name: str = Field(..., min_length=1, max_length=100, description="Group name")
    sub_groups: list[SubGroupSchema] | None = None


# Feature Validators
class ListFeaturesParamsValidator(BaseModel):
    """Validator for listing features query parameters"""
    group_id: str | None = Field(default=None, max_length=100, description="Filter by feature group ID")
    search_term: str | None = Field(default=None, max_length=100, description="Search in feature name")
    is_active: bool | None = Field(default=None, description="Filter by active status")
    is_addon: bool | None = Field(default=None, description="Filter by addon status")
    limit: int = Field(default=20, ge=1, le=300, description="Number of records to return")
    offset: int = Field(default=0, ge=0, description="Number of records to skip")


class FeatureOptionValidator(BaseModel):
    """Validator for feature options"""
    ilabel: str = Field(..., min_length=1, max_length=100, description="Item label")
    idata_type: str = Field(..., description="Item data type")
    ival_type: str = Field(..., description="Item value type")
    ival: str | int | bool = Field(..., description="Item value")


class CreateFeatureValidator(BaseModel):
    """Validator for creating features"""
    name: str = Field(..., min_length=1, max_length=100, description="Feature name")
    gid: str = Field(..., min_length=1, max_length=100, description="Group ID")
    gname: str = Field(..., min_length=1, max_length=100, description="Group name")
    sgid: str | None = Field(default=None, min_length=1, max_length=100, description="Group ID")
    sgname: str | None = Field(default=None, min_length=1, max_length=100, description="Group name")
    options: list[FeatureOptionValidator] = Field(..., min_items=1, description="Feature options")
    is_active: bool = Field(default=False, description="Whether the feature is active")
    is_kf: bool = Field(default=False, description="Whether it's a key feature")
    is_cfg: bool = Field(default=False, description="Whether it's configurable")
    has_bl: bool = Field(default=False, description="Whether it has business logic")
    is_bl_coded: bool = Field(default=False, description="Whether business logic is coded")
    track_usage: bool = Field(default=False, description="Whether to track usage")
    apply_disc: bool = Field(default=False, description="Whether to apply discount")
    is_addon: bool = Field(default=False, description="Whether it's an addon")
    addon_toggle: bool = Field(default=False, description="Whether addon has toggle")


class UpdateFeatureValidator(BaseModel):
    """Validator for updating features"""
    name: str = Field(..., min_length=1, max_length=100, description="Feature name")
    gid: str = Field(..., min_length=1, max_length=100, description="Group ID")
    gname: str = Field(..., min_length=1, max_length=100, description="Group name")
    sgid: str | None = Field(default=None, min_length=1, max_length=100, description="Group ID")
    sgname: str | None = Field(default=None, min_length=1, max_length=100, description="Group name")
    options: list[FeatureOptionValidator] = Field(..., min_items=1, description="Feature options")
    is_active: bool = Field(default=False, description="Whether the feature is active")
    is_kf: bool = Field(default=False, description="Whether it's a key feature")
    is_cfg: bool = Field(default=False, description="Whether it's configurable")
    has_bl: bool = Field(default=False, description="Whether it has business logic")
    is_bl_coded: bool = Field(default=False, description="Whether business logic is coded")
    track_usage: bool = Field(default=False, description="Whether to track usage")
    apply_disc: bool = Field(default=False, description="Whether to apply discount")
    is_addon: bool = Field(default=False, description="Whether it's an addon")
    addon_toggle: bool = Field(default=False, description="Whether addon has toggle")
