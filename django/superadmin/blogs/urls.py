from django.urls import path
from .views import (
    ListBlogsAPI,
    CountBlogsAPI,
    GetBlogDetailsAPI,
    CreateUpdateDeleteBlogAPI,
    ListTagsAPI,
    UploadBlogImageAPI
)

urlpatterns = [
    path('tags/', ListTagsAPI.as_view(), name='list_tags'),
    path('listing/', ListBlogsAPI.as_view(), name='list_blogs'),
    path('counts/', CountBlogsAPI.as_view(), name='count_blogs'),
    path('create/', CreateUpdateDeleteBlogAPI.as_view(), name='create_blog'),
    path('<str:blog_id>/details/', GetBlogDetailsAPI.as_view(), name='retrieve_blog'),
    path('<str:blog_id>/', CreateUpdateDeleteBlogAPI.as_view(), name='create_update_delete_blog'),
    path('<str:blog_id>/image/', UploadBlogImageAPI.as_view(), name='upload_delete_blog_image'),
]
