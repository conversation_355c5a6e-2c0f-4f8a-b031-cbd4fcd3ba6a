from pydantic import (
    BaseModel, Field,
    field_validator,
    model_validator
)
from typing_extensions import Self
from django.utils.text import slugify
from utils import DateUtil, estimate_read_time, get_uuid
from utils.constants import SAASProduct, BlogStatus


class BlogPayloadValidator(BaseModel):
    id: str = Field(default_factory=get_uuid)
    tags: list[str]
    product_id: SAASProduct = Field(..., description="product id")
    title: str
    slug: str = None
    desc: str
    image: str
    content: str
    uploaded_by: str
    blog_status: str = Field(default=BlogStatus.PUBLISHED.name, in_=[x.name for x in BlogStatus])
    blog_status_id: BlogStatus = Field(default=BlogStatus.PUBLISHED.value, description="blog status")
    read_time: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    @field_validator("tags", mode="after")
    def slugify_tags(cls, value: list[str] | None) -> str:
        if not value:
            raise ValueError(f"Please add tags.")
        value = [tag for tag in set([slugify(x) for x in value if x.strip()]) if tag]
        return value

    @model_validator(mode="after")
    def generate_slug_n_read_time(self) -> Self:
        if not self.title:
            raise ValueError(f"Invalid blog title")

        if not self.content:
            raise ValueError(f"Invalid blog content")

        self.slug = slugify(self.title)
        self.read_time = estimate_read_time(self.content)
        return self

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values
