import logging
from rest_framework import status
from rest_framework.views import APIView
from pydantic import ValidationError
from django.utils.decorators import method_decorator
from utils.mongo import MongoUtility
from utils.constants import (
    AdminDBColls,
    SAASProduct
)
from utils import (
    format_response,
    format_error_response,
    Memoization
)
from authn.decorators import validate_request_payload
from authn import AuthenticateSuperAdmin
from .request_validators import BlogPayloadValidator

logger = logging.getLogger('application')


class GetBlogDetailsAPI(APIView):

    def get(self, request, *args, **kwargs):
        db = MongoUtility()

        blog_id = kwargs.get('blog_id')

        blog = db.find(AdminDBColls.BLOGS, {'id': blog_id}, find_one=True)
        if not blog:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Blog not found.')
        return format_response(status.HTTP_200_OK, blog, 'Success')


@method_decorator(validate_request_payload(BlogPayloadValidator), name='post')
class CreateUpdateDeleteBlogAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def post(self, request, *args, **kwargs):
        db = MongoUtility()

        blog_data = request.validated_payload.model_dump()
        blog_id = blog_data['id']

        db.insert(AdminDBColls.BLOGS, [blog_data])
        blog_data.pop('_id', None)

        new_tags = [{'tag': tag, 'blog_id': blog_id} for tag in blog_data['tags']]
        db.insert(AdminDBColls.BLOG_TAGS, new_tags, insert_many=True)

        response_data = blog_data
        return format_response(status.HTTP_200_OK, response_data, 'Created Blog Successfully')

    def put(self, request, *args, **kwargs):
        blog_id = kwargs.get('blog_id')

        db = MongoUtility()

        blog = db.find(AdminDBColls.BLOGS, {'id': blog_id}, {'_id': 0, 'updated_on': 0})
        if not blog:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Blog not found.')

        try:
            blog.update(request.data)
            updated_data = BlogPayloadValidator(**blog).model_dump
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        updated_blog = db.update(AdminDBColls.BLOGS, {'id': blog_id}, updated_data, find_one_and_update=True)

        # delete and recreate blog tags to make sure there is no stale data
        db.delete(AdminDBColls.BLOG_TAGS, {'blog_id': blog_id}, delete_many=True)

        new_tags = [{'tag': tag, 'blog_id': blog_id} for tag in updated_blog['tags']]
        db.insert(AdminDBColls.BLOG_TAGS, new_tags, insert_many=True)

        return format_response(status.HTTP_200_OK, updated_blog, 'Blog updated successfully')

    def delete(self, request, *args, **kwargs):
        db = MongoUtility()

        blog_id = kwargs.get('blog_id')

        db.delete(AdminDBColls.BLOGS, {'id': blog_id})
        db.delete(AdminDBColls.BLOG_TAGS, {'blog_id': blog_id}, delete_many=True)

        return format_response(status.HTTP_200_OK, {}, 'Blog deleted successfully')


class ListTagsAPI(APIView):

    def get(self, request, *args, **kwargs):
        params = request.GET
        search = params.get('search')
        limit = int(params.get('limit') or 100)
        offset = int(params.get('offset') or 0)

        db = MongoUtility()

        query = {}
        if search:
            query['tag'] = {'$regex': search, '$options': 'i'}

        tags = db.distinct(AdminDBColls.BLOG_TAGS, 'tag', query)

        response_data = {'tags': tags[offset:offset + limit]}
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ListBlogsAPI(APIView):

    def get(self, request, *args, **kwargs):
        params = request.GET
        search = params.get('search')
        limit = int(params.get('limit') or 20)
        offset = int(params.get('offset') or 0)
        tags = [x for x in params.get('tags', '').split(',') if x]

        try:
            product_id = int(params.get('product_id'))
            if not product_id:
                raise ValueError
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid product_id')

        db = MongoUtility()

        query = {'product_id': product_id}
        if tags:
            query['tags'] = {'$in': tags}

        if search:
            query['$or'] = [
                {'title': {'$regex': search, '$options': 'i'}},
                {'desc': {'$regex': search, '$options': 'i'}},
                {'content': {'$regex': search, '$options': 'i'}},
            ]

        saas_products = Memoization.get_saas_product_details()

        product_blogs = db.find(AdminDBColls.BLOGS, query).limit(limit).skip(offset)
        total_product_blogs = product_blogs.count()
        product_blog_docs = [x for x in product_blogs]

        delta_blogs = []
        delta = limit - len(product_blog_docs)
        if delta > 0:
            remaining_product_ids = [x.value for x in SAASProduct if x.value != product_id]
            delta_offset = offset - ((total_product_blogs // limit) * limit)
            query['product_id'] = {'$in': remaining_product_ids}
            delta_blogs = db.find(AdminDBColls.BLOGS, query, sort=[('product_id', 1)]).limit(limit).skip(delta_offset)

        product_wise_blogs = {product_id: product_blog_docs}
        for blog in delta_blogs:
            product_wise_blogs.setdefault(blog['product_id'], []).append(blog)

        blogs_data = []
        for pid, blogs in product_wise_blogs.items():
            blogs_data.append({
                'product_id': pid,
                'product_name': saas_products[pid]['name'],
                'blogs': blogs
            })

        response_data = {'blogs': blogs_data}
        return format_response(status.HTTP_200_OK, response_data, 'Success')
