from pydantic import (
    BaseModel, Field
)
from utils.constants import SubscriptionStatus, SAASProduct


class ListSubscriptionsParamsValidator(BaseModel):
    status_id: int = Field(default=None, in_=[ss.value for ss in SubscriptionStatus])
    product_id: int = Field(default=None, in_=[sp.value for sp in SAASProduct])
    offset: int = 0
    limit: int = 20
    sort_by: str = '_id'
    sort_order: int = Field(default=1, description="Sort order must be either 1 (ascending) or -1 (descending)", in_=[1, -1])
