import logging
from rest_framework import status
from rest_framework.views import APIView
from django.utils.decorators import method_decorator
from authn.decorators import validate_query_params
from utils import (
    format_response,
    format_error_response,
)
from utils.constants import (
    AdminDBColls,
    SubscriptionStatus,
)
from utils.mongo import MongoUtility
from authn import AuthenticateSuperAdmin
from .request_validators import (
    ListSubscriptionsParamsValidator,
)

logger = logging.getLogger('application')


@method_decorator(validate_query_params(ListSubscriptionsParamsValidator), name='get')
class ListSubscriptionsAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        params = request.validated_params

        db = MongoUtility()

        if not params.status_id:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid status_id.')

        # Build filter query based on parameters
        filter_query = {'status_id': params.status_id}
        if params.product_id:
            filter_query['product_id'] = params.product_id

        # Define projection
        data_filter = {
            '_id': 0,
            'id': 1,
            'plan_name': 1,
            'product_name': 1,
            'company_id': 1,
            'status_id': 1,
            'status': 1,
            'is_annual_sub': 1,
            'created_on': 1,
            'updated_on': 1,
        }

        # Get total count for pagination
        total_count = db.find(AdminDBColls.SUBSCRIPTIONS, filter_query).count()

        # Get subscriptions with pagination
        sub_docs = db.find(
            AdminDBColls.SUBSCRIPTIONS,
            filter_query,
            data_filter,
            sort=[(params.sort_by, params.sort_order)]
        ).skip(params.offset).limit(params.limit)

        # Convert cursor to list
        company_ids = set()
        subscriptions = []
        for sub in sub_docs:
            company_ids.add(sub['company_id'])
            subscriptions.append(sub)

        df = {
            '_id': 0,
            'id': 1,
            'name': 1,
            'gstin': 1,
            'pan': 1,
            'address': 1,
            'city': 1,
            'state': 1,
            'pincode': 1,
            'email': 1,
            'phone': 1,
        }
        company_docs = {x.pop('id'): x for x in db.find(AdminDBColls.COMPANIES, {'id': {'$in': list(company_ids)}}, df)}
        for sub in subscriptions:
            try:
                sub.update(company_docs[sub['company_id']])
            except KeyError:
                pass

        # Prepare response data
        response_data = {
            'subscriptions': subscriptions,
            'total_count': total_count,
            'limit': params.limit,
            'offset': params.offset,
        }

        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_query_params(ListSubscriptionsParamsValidator), name='get')
class SubscriptionCountsAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        params = request.validated_params

        self.db = MongoUtility()

        query = {}
        if params.product_id:
            query['product_id'] = params.product_id

        response_data = {
            'totals': self.get_total_counts(query)
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def get_total_counts(self, query):
        query['status_id'] = {'$in': [x.value for x in SubscriptionStatus]}

        group_query = {
            "_id": "$status_id",
            "count": {"$sum": 1}
        }

        project_query = {
            "_id": 0,
            "status_id": "$_id",
            "count": 1
        }

        aggregate_result = list(self.db.aggregate(AdminDBColls.SUBSCRIPTIONS, query, group_query, project_query))
        return aggregate_result
