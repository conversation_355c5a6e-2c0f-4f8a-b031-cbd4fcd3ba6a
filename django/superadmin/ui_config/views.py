import logging
from rest_framework import status
from rest_framework.views import APIView
from utils import (
    format_response,
    format_error_response,
)
from utils.constants import (
    AdminDBColls
)
from utils.mongo import MongoUtility
from authn import AuthenticateSuperAdmin

logger = logging.getLogger('application')


class Components(object):
    SUPER_ADMIN_SUBS = 'super_admin_subs'


class UIConfig(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        params = request.GET
        self.request = request

        component = params.get('component')

        self.db = MongoUtility()

        try:
            config_func = getattr(self, f'get_{component}_config')
            data = config_func(component)
        except AttributeError:
            data = self.get_default_config(component)
            if not data:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    'Invalid component value recieved.',
                )

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_super_admin_sidebar_config(self, component):
        objs = self.get_data_from_db(component, find_one=False, sort=[('order', 1)])

        sidebar_options = []
        for obj in objs:
            option = {
                'id': obj['id'],
                'component_type': obj['component_type'],
                'name': obj['name'],
                'is_enabled': obj['is_enabled'],
            }

            for child_obj in obj.get('child_list', []):
                if not child_obj['is_active']:
                    continue

                child_option = {
                    'id': child_obj['id'],
                    'component_type': child_obj['component_type'],
                    'name': child_obj['name'],
                    'is_enabled': child_obj['is_enabled'],
                }

                if 'child_list' not in option:
                    option['child_list'] = [child_option]
                else:
                    option['child_list'].append(child_option)

            sidebar_options.append(option)
        return sidebar_options

    def get_data_from_db(self, component, coll=AdminDBColls.UI_CONFIG, find_one=True, sort=None):
        query = {
            'component': component,
            'is_active': True
        }
        return self.db.find(coll, query, find_one=find_one, sort=sort or [])

    def get_default_config(self, component):
        data = {'tabs': []}
        config = self.get_data_from_db(component)

        data['tabs'] = [{'id': x['id'], 'name': x['name']} for x in config.get('tabs', []) if x['is_active']]
        return data
