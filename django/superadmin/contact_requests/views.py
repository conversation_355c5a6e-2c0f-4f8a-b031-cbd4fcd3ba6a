import logging
from rest_framework import status
from pydantic import ValidationError
from rest_framework.views import APIView
from utils.common import AdminDBColls
from utils.mongo import MongoUtility
from utils import (
    format_error_response,
    format_response
)
from .request_validators import ContactRequestSubmitPayloadValidator

logger = logging.getLogger('application')


class ContactRequestSubmitApi(APIView):

    def post(self, request, *args, **kwargs):
        raw_payload = request.data

        try:
            payload = ContactRequestSubmitPayloadValidator(**raw_payload).model_dump()
        except ValidationError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                pydantic_error=e
            )

        db = MongoUtility()
        db.insert(AdminDBColls.CONTACT_REQUESTS, [payload])
        payload.pop('_id', None)

        from masters.tasks import send_contact_request_email
        send_contact_request_email(payload)

        return format_response(
            status.HTTP_200_OK,
            payload,
            'Contact request submitted successfully'
        )
