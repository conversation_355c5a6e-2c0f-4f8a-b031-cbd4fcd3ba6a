from pydantic import (
    BaseModel, Field
)
from utils.constants import AccountStatus


class ListCompaniesParamsValidator(BaseModel):
    acc_status_id: int | None = Field(default=None, in_=[x.value for x in AccountStatus])
    offset: int = 0
    limit: int = 20
    sort_by: str = '_id'
    sort_order: int = Field(default=1, description="Sort order must be either 1 (ascending) or -1 (descending)", in_=[1, -1])
