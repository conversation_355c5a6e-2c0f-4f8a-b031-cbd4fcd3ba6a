import logging
from rest_framework import status
from rest_framework.views import APIView
from django.utils.decorators import method_decorator
from authn.decorators import validate_query_params
from utils import (
    format_response,
    format_error_response,
    RedisUtils
)
from utils.constants import (
    AdminDBColls,
    AccountStatus,
    CompanyType,
    UserRole
)
from utils.mongo import MongoUtility
from authn import AuthenticateSuperAdmin
from .request_validators import (
    ListCompaniesParamsValidator,
)

logger = logging.getLogger('application')


@method_decorator(validate_query_params(ListCompaniesParamsValidator), name='get')
class ListCompaniesAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        params = request.validated_params

        db = MongoUtility()

        if not params.acc_status_id:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid acc_status_id.')

        df = {
            '_id': 0,
            'id': 1,
            'name': 1,
            'gstin': 1,
            'pan': 1,
            'address': 1,
            'city': 1,
            'state': 1,
            'pincode': 1,
            'email': 1,
            'phone': 1,
            'acc_status_id': 1,
            'acc_status': 1,
            'created_on': 1,
        }

        # Build filter query based on parameters
        query = {'acc_status_id': params.acc_status_id}

        # Get total count for pagination
        total_count = db.find(AdminDBColls.COMPANIES, query).count()

        # Get companies with pagination
        sort_list = [(params.sort_by, params.sort_order)]
        company_docs = db.find(AdminDBColls.COMPANIES, query, df, sort=sort_list).skip(params.offset).limit(params.limit)

        companies, company_ids = [], []
        company_index_map = {}
        for i, company in enumerate(company_docs):
            company_index_map[company['id']] = i
            company_ids.append(company['id'])
            companies.append(company)

        query = {
            'company_id': {'$in': company_ids},
            'company_type': CompanyType.SEEKER.value,
            'user_role': UserRole.ADMIN_SEEKER.value
        }
        df = {
            '_id': 0,
            'company_id': 1,
            'user_name': 1,
            'email': 1,
            'phone': 1,
        }
        admin_users = db.find(AdminDBColls.USERS, query, df)
        for user in admin_users:
            company_id = user.pop('company_id')
            companies[company_index_map[company_id]]['admin_details'] = user

        # Prepare response data
        response_data = {
            'companies': companies,
            'total_count': total_count,
            'limit': params.limit,
            'offset': params.offset,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


@method_decorator(validate_query_params(ListCompaniesParamsValidator), name='get')
class CompanyCountsAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def get(self, request):
        self.db = MongoUtility()

        query = {}

        response_data = {
            'totals': self.get_total_counts(query)
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def get_total_counts(self, query):
        query['acc_status_id'] = {'$in': [x.value for x in AccountStatus]}

        group_query = {
            "_id": "$acc_status_id",
            "count": {"$sum": 1}
        }

        project_query = {
            "_id": 0,
            "acc_status_id": "$_id",
            "count": 1
        }

        aggregate_result = list(self.db.aggregate(AdminDBColls.COMPANIES, query, group_query, project_query))
        return aggregate_result


class ApproveOrRejectCompanyAPI(APIView):
    authentication_classes = (AuthenticateSuperAdmin, )

    def post(self, request, *args, **kwargs):
        payload = request.data
        company_id = kwargs['company_id']

        db = MongoUtility()

        query = {'id': company_id}
        company = db.find(AdminDBColls.COMPANIES, query, find_one=True)
        if not company:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Company not found.')

        if company['acc_status_id'] != AccountStatus.IN_REVIEW.value:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Company is not under review.')

        update_query = {}
        if payload.get('is_approved'):
            update_query = {
                'acc_status_id': AccountStatus.ACTIVE.value,
                'acc_status': AccountStatus.ACTIVE.name,
            }
        elif payload.get('is_rejected'):
            default_remarks = 'Background verification failed.'
            update_query = {
                'acc_status_id': AccountStatus.REJECTED.value,
                'acc_status': AccountStatus.REJECTED.name,
                'remarks': payload.get('remarks') or default_remarks,
            }

        if update_query:
            update_query['updated_on'] = request.now
            company = db.update(AdminDBColls.COMPANIES, query, update_query, find_one_and_update=True)

            from masters.tasks import send_account_review_email
            send_account_review_email(company)

            self.update_session_data(company_id, payload.get('is_approved'))

        return format_response(status.HTTP_200_OK, {}, 'Success')

    def update_session_data(self, company_id: str, is_approved: bool = False):
        """
        Update session data in both database and cache for the given email

        Args:
            email (str): User's email address
            update_data (dict): Data to update in the session
        """
        if not is_approved:
            return

        db = MongoUtility()

        query = {'company_id': company_id}

        # Find user session in database
        user_session = db.find(AdminDBColls.USER_SESSIONS, query, find_one=True)
        if not user_session:
            return

        update_data = {'is_acc_active': is_approved}

        # Update session in database
        db.update(AdminDBColls.USER_SESSIONS, query, update_data)

        # Update session in Redis cache
        token = user_session['token']
        redis_inst = RedisUtils()
        session_data = redis_inst.get_data(token)
        if session_data:
            session_data.update(update_data)
            redis_inst.set_data(token, session_data)
