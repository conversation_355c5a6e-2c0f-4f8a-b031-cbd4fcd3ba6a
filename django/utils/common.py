import re
import time
import json
import hashlib
import logging
import requests
import traceback
from uuid import uuid4
from urllib import parse
from django.conf import settings
from .mongo import MongoUtility
from .constants import AdminDBColls
from .date_util import DateUtil


logger = logging.getLogger('application')


def get_uuid():
    return str(uuid4()).replace('-', '')


def is_valid_domain(email):
    # import socket
    import dns.resolver
    from disposable_email_domains import blocklist

    try:
        domain = email.split('@')[1]
        # socket.gethostbyname(domain)
        dns.resolver.resolve(domain, 'MX')
        if settings.ENVIRONMENT_ENV == 'prod':
            return domain not in blocklist
        else:
            return True
    except Exception:
        return False


def get_domain_from_url(url):
    parsed_url = parse.urlsplit(url)
    return parsed_url.netloc


def get_parsed_url_data(url):
    data = {}
    parsed_obj = parse.urlsplit(url)
    scheme, host = parsed_obj.scheme, parsed_obj.netloc
    if scheme and host:
        data.update({
            'origin': '{}://{}'.format(scheme, host),
            'scheme': scheme,
            'host': host,
            'path': parsed_obj.path,
        })
    return data


def get_client_details(request):
    meta_data = request.META
    meta_keys = [
        'HTTP_X_FORWARDED_FOR',
        'REMOTE_ADDR',
        'HTTP_HOST',
        'REQUEST_METHOD',
        'PATH_INFO',
        'SERVER_PROTOCOL',
        'CONTENT_TYPE',
        'HTTP_USER_AGENT',
    ]

    details = {key.lower(): meta_data.get(key) for key in meta_keys}

    if details['http_x_forwarded_for']:
        details['ip'] = details['http_x_forwarded_for'].split(',')[0]
    else:
        details['ip'] = details['remote_addr']
    return details


def generate_hash_id(data):
    if isinstance(data, dict):
        data_to_hash = json.dumps(data, sort_keys=True)
    else:
        data_to_hash = re.sub(r'[^a-z0-9]', '', data.lower())

    hash_id = hashlib.md5(data_to_hash.encode()).hexdigest()  # 32-character hash
    return hash_id


def get_traceback(e):
    try:
        return ''.join(traceback.TracebackException.from_exception(e).format())
    except Exception as err:
        return 'Error occurred while fetching traceback of original error ({}). {}'.format(e, err)


def fetch_response(request_url, params=None, headers=None, method='GET', payload='', timeout=30):
    if headers and ('cache-control' not in headers):
        headers.update({'cache-control': 'no-cache'})
    return requests.request(method, request_url, data=payload, headers=headers, params=params, timeout=timeout)


def execute_task_in_thread(target, log_msg, args=(), **kwargs):
    from threading import Thread
    logger.info(log_msg)

    delay = int(kwargs.get('delay') or 0)
    if delay:
        time.sleep(delay)

    thread_inst = Thread(target=target, args=args)
    thread_inst.start()
    # logger.info(f'After sap trigger.. {thread_inst.is_alive()}')
    return thread_inst


def log_api_request(api_type, request, company_id=None, logs_coll=AdminDBColls.API_REQUEST_LOGS, **kwargs):
    try:
        db = MongoUtility()

        try:
            request_method = request.method
        except AttributeError:
            request_method = 'GET'

        request_body = None
        if request_method.lower() == 'post':
            try:
                request_body = kwargs.get('request_payload', {})
                if isinstance(request_body, str):
                    try:
                        request_body = json.loads(request_body)
                    except ValueError:
                        pass
            except (AttributeError, ValueError):
                request_body = {}

        try:
            url = request.get_raw_uri()
        except AttributeError:
            url = None  # noqa

        obj = {
            'api_type': api_type.value,
            'company_id': company_id,
            'api_name': api_type.name,
            'request_url': url,
            'request_method': request_method,
            'request_body': request_body,
            # 'request_headers': headers,
            # 'response_status_code': 200,
            # 'response_body': raw_response,
            # 'response_type': response_type,
            'created_on': DateUtil.get_current_timestamp(),
            'datetime': DateUtil.get_current_timestamp(True),
            **kwargs
        }
        db.insert(logs_coll, [obj])
    except Exception as e:
        logger.error('[Unhandled Exception] {}'.format(get_traceback(e)))
        return False
    return True


class DummyClass(object):

    def __init__(self, dict_obj={}):
        for key, value in dict_obj.items():
            setattr(self, key, value)
