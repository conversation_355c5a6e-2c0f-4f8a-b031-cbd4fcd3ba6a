from pymongo import (
    MongoClient,
    Insert<PERSON>ne,
    UpdateOne,
    UpdateMany,
    # ReplaceOne,
    DeleteOne,
    DeleteMany,
    ReturnDocument,
)
from django.conf import settings
from .constants import SAASModules


def test_mongo_connection(mongo_uri):
    from pymongo import MongoClient
    from pymongo.server_api import Server<PERSON><PERSON>

    # Create a new client and connect to the server
    client = MongoClient(mongo_uri, server_api=ServerApi('1'))

    # Send a ping to confirm a successful connection
    try:
        client.admin.command('ping')
        print("Pinged your deployment. You successfully connected to MongoDB!")
    except Exception as e:
        print(e)


class MongoConnection:

    mongo_client_mapping = {
        SAASModules.SUPER_ADMIN.value: None,
        SAASModules.ADMIN.value: None,
        SAASModules.PROCUREMENT.value: None,
        SAASModules.VENDOR_ONBOARDING.value: None,
        SAASModules.VENDOR_EXPLORER.value: None,
        SAASModules.FREIGHT_INDEX.value: None,
        SAASModules.CARBON_EMISSIONS.value: None,
    }
    previous_db_url = None
    mongo_client = None

    @staticmethod
    def get_db(
        db_url=settings.SAAS_ADMIN_DB_URL,
        db_name=settings.SAAS_ADMIN_DB_NAME,
        w='majority',
        read_preference='primary',
        retry_writes=True,
        app_name='saas_admin',
        module_id=None,
        get_new_conn=False
    ):
        previous_db_url = MongoConnection.previous_db_url
        module_mongo_client = MongoConnection.mongo_client_mapping.get(module_id)
        mongo_client = MongoConnection.mongo_client

        if not get_new_conn:
            if module_mongo_client is not None:
                return module_mongo_client

            if (mongo_client is not None) and (previous_db_url == db_url):
                return mongo_client

        db_url = db_url.split('?')[0]

        connection_params = {
            'tls': settings.USE_SSL,
            'readPreference': read_preference,
            'w': w,
            'retryWrites': retry_writes,
            'appName': app_name,
            # 'maxPoolSize': 150  # default = 100
        }
        mongo_client = MongoClient(db_url, **connection_params)

        if module_id:
            MongoConnection.mongo_client_mapping[module_id] = mongo_client
        else:
            MongoConnection.previous_db_url = db_url
            MongoConnection.mongo_client = mongo_client
        return mongo_client


class MongoUtility(object):

    db_mapping = {
        SAASModules.SUPER_ADMIN.value: {'db_url': settings.SAAS_ADMIN_DB_URL, 'db_name': settings.SAAS_ADMIN_DB_NAME},
        SAASModules.ADMIN.value: {'db_url': settings.SAAS_ADMIN_DB_URL, 'db_name': settings.SAAS_ADMIN_DB_NAME},
        SAASModules.PROCUREMENT.value: {'db_url': settings.SAAS_PROCUREMENT_DB_URL, 'db_name': settings.SAAS_PROCUREMENT_DB_NAME},
        SAASModules.VENDOR_ONBOARDING.value: {'db_url': settings.SAAS_NETWORK_DB_URL, 'db_name': settings.SAAS_NETWORK_DB_NAME},
        SAASModules.VENDOR_EXPLORER.value: {'db_url': settings.SAAS_NETWORK_DB_URL, 'db_name': settings.SAAS_NETWORK_DB_NAME},
        SAASModules.FREIGHT_INDEX.value: {'db_url': settings.SAAS_FREIGHT_INDEX_DB_URL, 'db_name': settings.SAAS_FREIGHT_INDEX_DB_NAME},
        SAASModules.CARBON_EMISSIONS.value: {'db_url': settings.SAAS_FREIGHT_INDEX_DB_URL, 'db_name': settings.SAAS_FREIGHT_INDEX_DB_NAME},
    }

    def __init__(
        self,
        db_url=settings.SAAS_ADMIN_DB_URL,
        db_name=settings.SAAS_ADMIN_DB_NAME,
        w='majority',
        read_preference='primary',
        retry_writes=True,
        app_name='saas_admin',
        module_id=None,
        get_new_conn=False
    ):

        args = {
            'db_url': db_url,
            'db_name': db_name,
            'w': w,
            'read_preference': read_preference,
            'retry_writes': retry_writes,
            'app_name': app_name,
            'module_id': module_id,
            'get_new_conn': get_new_conn,
        }

        if module_id:
            db_data = MongoUtility.db_mapping[module_id]
            args.update(**db_data)

        self.client = MongoConnection.get_db(**args)
        self.db = self.client[args['db_name']]
        self.bulk_ops = []

    def get_collection(self, collection_name):
        return self.db[collection_name]

    @staticmethod
    def _get_result(response):
        return {
            'acknowledged': response.acknowledged,
            'matched_count': response.matched_count,
            'modified_count': response.modified_count,
            'upserted_id': response.upserted_id,
        }

    def find(self, collection_name, filter_query, data_filter={'_id': 0}, sort=[], find_one=False):
        coll = self.db[collection_name]
        if find_one:
            return coll.find_one(filter_query, data_filter, sort=sort) or {}

        queryset = coll.find(filter_query, data_filter, sort=sort)

        def count(query=filter_query):
            return coll.count_documents(query)

        setattr(queryset, 'count', count)
        return queryset

    def aggregate(self, collection_name, match_query={}, group_query={}, project_query={}, sort_query={}, pipeline=None, allow_disk_use=False):
        coll = self.db[collection_name]
        if pipeline:
            return coll.aggregate(pipeline, allowDiskUse=allow_disk_use)

        pipeline = []
        if match_query:
            pipeline.append({'$match': match_query})
        if group_query:
            pipeline.append({'$group': group_query})
        if sort_query:
            pipeline.append({'$sort': sort_query})
        if project_query:
            pipeline.append({'$project': project_query})
        return coll.aggregate(pipeline, allowDiskUse=allow_disk_use)

    def distinct(self, collection_name, field_name, filter_query={}):
        coll = self.db[collection_name]
        return list(coll.distinct(field_name, filter_query))

    def update(
        self, collection_name, filter_query,
        set_query={}, push_query={}, pull_query={},
        inc_query={}, unset_query={}, array_filters=[],
        update_many=False, upsert=False,
        find_one_and_update=False, data_filter={'_id': 0}, sort=None,
        return_document=ReturnDocument.AFTER
    ):
        coll = self.db[collection_name]
        update_query = {}
        if set_query:
            update_query['$set'] = set_query
        if unset_query:
            update_query['$unset'] = unset_query
        if push_query:
            update_query['$push'] = push_query
        if pull_query:
            update_query['$pull'] = pull_query
        if inc_query:
            update_query['$inc'] = inc_query

        if update_many:
            response = coll.update_many(filter_query, update_query, array_filters=array_filters, upsert=upsert)
            return self._get_result(response)
        elif find_one_and_update:
            return coll.find_one_and_update(
                filter_query, update_query,
                projection=data_filter,
                array_filters=array_filters,
                sort=sort, upsert=upsert,
                return_document=return_document
            )
        response = coll.update_one(filter_query, update_query, array_filters=array_filters, upsert=upsert)
        return self._get_result(response)

    def insert(self, collection_name, new_documents=[], insert_many=False):
        coll = self.db[collection_name]
        if insert_many:
            return coll.insert_many(new_documents)
        return coll.insert_one(new_documents[0])

    def delete(self, collection_name, filter_query, delete_many=False):
        coll = self.db[collection_name]
        if filter_query:
            if delete_many:
                return coll.delete_many(filter_query)
            return coll.delete_one(filter_query)

    def list_indexes(self, collection_name):
        coll = self.db[collection_name]
        return list(coll.list_indexes())

    def create_index(self, collection_name, index_items=[], **kwargs):
        coll = self.db[collection_name]
        if index_items:
            return coll.create_index(index_items, **kwargs)
        return None

    def list_collections(self):
        return self.db.list_collection_names()

    def add_write_operation(
        self,
        filter_query={},
        set_query={}, push_query={}, pull_query={}, inc_query={}, unset_query={},
        insert_one={},
        update_one=False,
        update_many=False,
        upsert=False,
        delete_one=False,
        delete_many=False,
    ):
        op = None
        update_query = {}
        if update_one or update_many:
            if set_query:
                update_query['$set'] = set_query
            # if unset_query:
            #     update_query['$unset'] = unset_query
            # if push_query:
            #     update_query['$push'] = push_query
            # if pull_query:
            #     update_query['$pull'] = pull_query
            # if inc_query:
            #     update_query['$inc'] = inc_query

        if update_one:
            op = UpdateOne(filter=filter_query, update=update_query, upsert=upsert)
        elif update_many:
            op = UpdateMany(filter=filter_query, update=update_query, upsert=upsert)
        elif insert_one:
            op = InsertOne(document=insert_one)
        elif delete_one:
            op = DeleteOne(filter=filter_query)
        elif delete_many:
            op = DeleteMany(filter=filter_query)

        if op:
            self.bulk_ops.append(op)

    def bulk_write(self, collection_name, ordered=True):
        result = {}
        if self.bulk_ops:
            coll = self.db[collection_name]
            result = coll.bulk_write(self.bulk_ops, ordered=ordered).bulk_api_result
            self.bulk_ops = []
        return result
