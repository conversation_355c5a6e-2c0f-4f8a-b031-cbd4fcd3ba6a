from utils.mongo import MongoUtility
from utils.constants import (
    CarbonEmissionColls,
    FreightIndexDBColls,
    AdminDBColls,
    SAASModules
)
from .custom_exceptions import ConfigError


class Memoization:
    sms_vendor_configs = {}
    billing_cycles = {}
    app_domains = {}
    app_paths = {}
    tooltips = {}
    dropdowns_data = {}
    api_keys = {}
    api_configs = {}
    sagemaker_model_endpoints = {}
    saas_products = {}
    emission_settings = {}
    emission_vehicle_types = {}

    @staticmethod
    def get_sms_vendor_config(vendor_type):
        if vendor_type in Memoization.sms_vendor_configs:
            return Memoization.sms_vendor_configs[vendor_type]

        db = MongoUtility()
        config = db.find(AdminDBColls.SMS_VENDORS, {'id': vendor_type}, find_one=True)
        Memoization.sms_vendor_configs[vendor_type] = config
        return config

    @staticmethod
    def get_billing_cycles():
        if not Memoization.billing_cycles:
            db = MongoUtility()
            objs = db.find(AdminDBColls.BILLING_CYCLES, {'is_active': True}, {'name': 1, 'id': 1}, sort=[('index', 1)])
            Memoization.billing_cycles = {x['id']: x['name'] for x in objs}
        return Memoization.billing_cycles

    @staticmethod
    def get_api_key(module_id: int, service_key: str = 'google_api_key_be') -> str:
        if service_key not in Memoization.api_keys:
            module = SAASModules(module_id)

            db = MongoUtility()
            doc = db.find(AdminDBColls.API_KEYS, {'module_id': module.value}, find_one=True)
            try:
                Memoization.api_keys[service_key] = doc[service_key]
            except KeyError:
                raise ConfigError(f'Api Key not configured for {module.name} - {service_key}')
        return Memoization.api_keys[service_key]

    @staticmethod
    def get_api_config(api_type, module_id):
        if api_type not in Memoization.api_configs:
            db = MongoUtility()
            query = {
                # 'module_id': module_id,
                'api_type': api_type.value,
                'is_active': True,
            }
            config = db.find(AdminDBColls.API_CONFIGS, query, find_one=True)
            if not config:
                raise ConfigError(f'Api details not configured for {api_type.value} - {api_type.name}')
            Memoization.api_configs[api_type] = config
        return Memoization.api_configs[api_type]

    @staticmethod
    def get_app_url_data(domain_id, path_id):
        try:
            domain = Memoization.app_domains[domain_id]
            path = Memoization.app_paths[path_id]
        except KeyError:
            db = MongoUtility()
            obj = db.find(AdminDBColls.APP_URLS, {'id': domain_id}, find_one=True)

            if domain_id not in Memoization.app_domains:
                Memoization.app_domains[domain_id] = obj['fe_domain'].strip('/')

            for key, value in obj['fe_paths'].items():
                if key not in Memoization.app_paths:
                    Memoization.app_paths[key] = value

            domain = Memoization.app_domains[domain_id]
            path = Memoization.app_paths[path_id]

        return {
            'domain': domain,
            'path': path,
            'url': f'{domain}{path}',
        }

    @staticmethod
    def get_saas_product_details(product_id):
        if product_id in Memoization.saas_products:
            return Memoization.saas_products[product_id]
        db = MongoUtility()
        for product in db.find(AdminDBColls.SAAS_PRODUCTS, {}):
            Memoization.saas_products[product['id']] = product
        if product_id not in Memoization:
            raise ValueError(f'Invalid product_id - {product_id}')
        return Memoization.saas_products[product_id]

    @staticmethod
    def get_tooltips(product_id):
        db = MongoUtility()
        if product_id not in Memoization.tooltips:
            Memoization.tooltips[product_id] = {x['id']: x['tooltip'] for x in db.find(AdminDBColls.TOOLTIPS, {})}
        return Memoization.tooltips[product_id]

    @staticmethod
    def get_dropdown_data(dropdown_coll, sort_by='name', sort_order=1):
        if not Memoization.dropdowns_data.get(dropdown_coll):
            db = MongoUtility()
            df = {'_id': 0, 'is_active': 0, 'order': 0}
            Memoization.dropdowns_data[dropdown_coll] = [x for x in db.find(dropdown_coll, {'is_active': True}, df, sort=[('order', 1), (sort_by, sort_order)])]
        return Memoization.dropdowns_data[dropdown_coll]

    @staticmethod
    def get_sagemaker_model(module_id: int, model_id: int, action: str) -> dict:
        db = MongoUtility(module_id=module_id)
        if (model_id, action) not in Memoization.sagemaker_model_endpoints:
            config = db.find(FreightIndexDBColls.SAGEMAKER_MODELS, {'model_id': model_id, 'action_name': action}, find_one=True)

            Memoization.sagemaker_model_endpoints[(model_id, action)] = config
        return Memoization.sagemaker_model_endpoints[(model_id, action)]

    @staticmethod
    def get_emission_settings():
        if not Memoization.emission_settings:
            db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
            load_types = list(db.find(CarbonEmissionColls.LOAD_TYPES, {}, sort=[('id', 1)]))
            mode_types = list(db.find(CarbonEmissionColls.MODE_TYPES, {}, sort=[('id', 1)]))
            vehicle_types = list(db.find(CarbonEmissionColls.VEHICLE_TYPES, {}, sort=[('id', 1)]))

            data = {
                'load_types': load_types,
                'mode_types': mode_types,
                'vehicle_types': vehicle_types
            }
            Memoization.emission_settings = data
        return Memoization.emission_settings

    @staticmethod
    def get_emission_vehicle_types(mode_type):
        if mode_type not in Memoization.emission_vehicle_types:
            db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
            vehicle_types = list(
                db.find(
                    CarbonEmissionColls.VEHICLE_TYPES,
                    {'mode_type': mode_type},
                    sort=[('id', 1)]
                )
            )
            Memoization.emission_vehicle_types[mode_type] = vehicle_types
        return Memoization.emission_vehicle_types[mode_type]
