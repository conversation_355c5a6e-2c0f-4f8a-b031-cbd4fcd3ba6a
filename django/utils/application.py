import jwt
import json
import pickle
import redis
import logging
from datetime import timedelta
from django.conf import settings
from .common import get_domain_from_url
from .date_util import DateUtil

logger = logging.getLogger('application')


def get_next_reset_date(subscribed_on, interval_days=30):
    if not subscribed_on:
        return None

    start_date = DateUtil.convert_to_datetime(subscribed_on)
    today = DateUtil.get_current_timestamp(get_date_obj=True)

    # Keep adding 30 days until we go past today
    next_date = start_date
    while next_date <= today:
        next_date += timedelta(days=interval_days)

    return DateUtil.convert_to_unix(next_date)


class JWTToken:
    HS256 = 'HS256'

    class Expiry:
        USER_INVITE = 10080  # 7days

    class Purpose:
        ACTIVATE_USER = 'activate_user'

    @staticmethod
    def get_token(payload: dict, subject: str, purpose: str, expiry: int) -> str:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        issued_at = int(DateUtil.get_current_timestamp() / 1000)
        expires_on = issued_at + (expiry * 60)
        payload.update({
            "iss": issuer,
            "sub": subject,
            "iat": issued_at,
            "exp": expires_on,
            "purpose": purpose,  # custom claim for clarity
        })
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm=JWTToken.HS256)
        return token

    @staticmethod
    def decode_token(token: str, issuer: str = settings.INTERNAL_SERVER_URL, secret: str = settings.SECRET_KEY) -> dict:
        issuer = get_domain_from_url(settings.INTERNAL_SERVER_URL)
        options = {"require": ["iss", "sub", "iat", "exp"]}  # required claims

        try:
            decoded_payload = jwt.decode(token, secret, issuer=issuer, options=options, algorithms=[JWTToken.HS256])
        except (jwt.ExpiredSignatureError, jwt.InvalidIssuerError, jwt.InvalidAudienceError, jwt.MissingRequiredClaimError) as e:
            raise ValueError(f'Token error: {e}')
        except jwt.exceptions.DecodeError:
            raise ValueError(f'Invalid token')
        return decoded_payload


class RedisUtils(object):

    def __init__(self, db_url: str = settings.REDIS_SESSION_DB_URL):
        self.redis_conn = redis.from_url(db_url)

    def list_keys(self):
        """List all keys from current DB."""
        return self.redis_conn.keys('*')

    def get_data(self, key: str) -> dict:
        """Get data stored using JSON format."""
        data = self.redis_conn.get(key)
        return json.loads(data) if data else {}

    def get_pdata(self, key: str) -> dict:
        """Get data stored using Pickle format."""
        data = self.redis_conn.get(key)
        return pickle.loads(data) if data else {}

    def set_data(self, key: str, data: dict, expiry: int = None) -> bool:
        """Set data using JSON format."""
        return self.redis_conn.set(key, json.dumps(data), ex=expiry)

    def set_pdata(self, key: str, data: dict, expiry: int = None) -> bool:
        """Set data using Pickle format."""
        return self.redis_conn.set(key, pickle.dumps(data), ex=expiry)

    def delete_data(self, key: str = None, flush_db: bool = False, flush_all: bool = False) -> int:
        """Delete specific key data or flush_db (current DB) or flush_all (data in all DBs)."""
        res = 0
        if key:
            res = self.redis_conn.delete(key)
        elif flush_db:
            res = self.redis_conn.flushdb()
        elif flush_all:
            res = self.redis_conn.flushall()
        return res
