import boto3
import urllib
import logging
from django.conf import settings
from .common import get_traceback

logger = logging.getLogger('application')

JPG_CONTENT_TYPE = 'image/jpeg'
MSWORD_CONTENT_TYPE = 'application/msword'
CONTENT_TYPE_MAPPING = {
    'pdf': 'application/pdf',
    'png': 'image/png',
    'jpeg': JPG_CONTENT_TYPE,
    'jpg': JPG_CONTENT_TYPE,
    'docx': MSWORD_CONTENT_TYPE,
    'doc': MSWORD_CONTENT_TYPE,
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}


def upload_to_s3(file, file_path, s3_bucket, s3_region=None, format_type=''):
    file_url, error = '', None
    file_name = None

    def get_content_type(file_name):
        try:
            format_type = file_name.rsplit('.', 1)[-1].lower()
        except (IndexError, AttributeError):
            format_type = None
        return CONTENT_TYPE_MAPPING.get(format_type, 'binary/octet-stream')

    try:
        s3 = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )

        # Get the bucket location (region)
        region = s3.get_bucket_location(Bucket=s3_bucket)['LocationConstraint']
        if region is None:
            region = s3_region  # Default region for S3

        # Construct the region host
        region_host = f's3.{region}.amazonaws.com'

        s3_path = file_path.strip('/')
        if isinstance(file, str):
            # Case when file is being uploaded directly from the disk
            file_name = file.rsplit("/", 1)[-1]
            content_type = get_content_type(file_name)

            # Open the local file as a file-like object
            with open(file, 'rb') as file_obj:
                # Upload the file-like object to S3
                s3.put_object(Body=file_obj, Bucket=s3_bucket, ContentType=content_type, Key=s3_path)
        else:
            file_name = file.name
            content_type = get_content_type(file_name)

            try:
                bytes_data = file.file.getvalue()
            except AttributeError:
                # when multiple files are uploaded at once
                file.file.file.seek(0)
                bytes_data = file.file.file.read()

            s3.put_object(Body=bytes_data, Bucket=s3_bucket, ContentType=content_type, Key=s3_path)

        # need to encode the file path as the link generated in S3 is encoded.
        file_url = 'https://{}/{}/{}'.format(
            region_host,
            s3_bucket,
            urllib.parse.quote_plus(s3_path, safe='/')
        )
    except Exception as e:
        logger.error(f"[S3_FILE_UPLOAD_ERROR] | {str(e)} | {get_traceback(e)}")
        error = 'Error uploading the file ({}) [{}]'.format(file_name, e)
    return file_url, error


def delete_from_s3(file_url, s3_bucket, s3_region):
    error = None
    try:
        s3 = boto3.resource(
            's3',
            region_name=s3_region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
        )
        s3_path = file_url.split(s3_bucket, 1)[-1].strip('/')
        s3_path = urllib.parse.unquote_plus(s3_path)
        s3.Object(s3_bucket, s3_path).delete()
    except Exception as e:
        logger.error(f"[S3_FILE_DELETE_ERROR] Error deleting the file at {s3_path} | {str(e)} | {get_traceback(e)}")
        error = f'Error deleting the file at {s3_path} | {e}'
    return error
