import logging
from utils.constants import ApiType, SAASModules
from utils.memoization import Memoization
from utils.common import fetch_response
from utils.date_util import DateUtil

logger = logging.getLogger('application')

def get_currency_rates():
    api_type = ApiType.EXCHANGE_RATES
    module_id = SAASModules.SUPER_ADMIN.value
    api_config = Memoization.get_api_config(api_type, module_id)
    api_key = Memoization.get_api_key(module_id, service_key='exchange_rates_api_key_be')

    params = {'access_key': api_key}

    response = fetch_response(
        api_config['api'], params,
        headers=api_config['headers'],
        method=api_config['method']
    )

    try:
        response_data = response.json()
    except Exception as e:
        logger.error(f'Failed to parse api({api_type.name}) response data: {e}')
        response_data = {}

    is_success = (response_data and ('error' not in response_data))
    return response_data, is_success


def make_currency_vs_rates(quotes, created_on=None):
    if not created_on:
        now = DateUtil.get_current_timestamp()
        created_on = DateUtil.convert_to_datetime(now)
        created_on = DateUtil.format_timestamp(created_on, str_format='%Y%m%d')
        created_on = int(created_on)

    common_quotes = [
        {'id': 1, 'name': 'USD', 'value': 1},
        {'id': 2, 'name': 'INR', 'value': quotes.pop('USDINR')},
        {'id': 3, 'name': 'EUR', 'value': quotes.pop('USDEUR')},
    ]
    currency_codes = list(quotes.keys())

    other_quotes = []
    for i, k in enumerate(currency_codes, 4):
        other_quotes.append({
            'id': i,
            'name': k.lstrip('USD'),
            'value': quotes[k]
        })

    all_quotes = common_quotes + other_quotes

    currency_vs_rates = {
        'created_on': created_on,
        'quotes': all_quotes
    }
    return currency_vs_rates
