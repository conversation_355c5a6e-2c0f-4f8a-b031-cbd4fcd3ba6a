from enum import Enum
from zoneinfo import ZoneInfo

IST_TZ = ZoneInfo('Asia/Kolkata')
UTC_TZ = ZoneInfo('UTC')


class ImageTypes(Enum):
    JPG = 'jpg'
    JPEG = 'jpeg'
    PNG = 'png'


class DocumentTypes(Enum):
    PDF = 'pdf'
    XLSX = 'xlsx'
    XLS = 'xls'
    DOCX = 'docx'
    DOC = 'doc'


class VideoTypes(Enum):
    MP4 = 'mp4'
    MOV = 'mov'
    WEBM = 'webm'


# Dynamically create FileTypes Enum
def create_combined_enum(name, *enums):
    combined = {item.name: item.value for enum in enums for item in enum}
    return Enum(name, combined)


FileTypes = create_combined_enum("FileTypes", ImageTypes, DocumentTypes, VideoTypes)


class SuccessMessages:
    SUCCESS = 'Success'
    OKAY = 'Looks like everything went okay'
    UPDATE_SUCCESS = 'Data updated successfully'
    RETRIEVE_SUCCESS = 'Data retrieved successfully'


class ErrorMessages:
    INVALID_TOKEN = 'Invalid token'
    ACCESS_DENIED = 'Access Denied'
    TECHNICAL_ERROR = 'OOPS!! Something went wrong. Please try again after sometime.'
    SMS_ALERT = '[SMS_ALERT_ERROR]'
    EMAIL_ALERT = '[EMAIL_ALERT_ERROR]'
    NEW_PASSWORD_AND_CONFIRM_PASSWORD_MISMACTH = 'New password & Confirm password are not same.'


class AppDomainID:
    AUTH = 'auth'
    ADMIN = 'admin'
    SUPERADMIN = 'superadmin'
    PROCUREMENT = 'procurement'
    NETWORK = 'network'


class AppPathID:
    # auth paths
    AUTH_LOGIN = 'auth_login'
    AUTH_ACTIVATE_ACCOUNT = 'auth_activate_account'
    # superadmin paths
    SADMIN_CONFIG = 'sadmin_config'
    # admin paths
    ADMIN_CONFIRM_PLAN = 'admin_confirm_plan'
    ADMIN_PROCEED_TO_PAY = 'admin_proceed_to_pay'
    ADMIN_PROFILE = 'admin_profile'
    # procurement paths
    PROCUREMENT_RFQ_DASHBOARD = 'procurement_rfq_dashboard'


class EmailHeader(Enum):
    USER_INVITE = 'SCLEN Invitation'
    NOTIFICATION = 'SCLEN Notification'
    CONTACT_REQUEST = 'SCLEN New Contact Request'
    BOOK_A_DEMO = 'SCLEN New Demo Request'
    ACCOUNT_REVIEW = 'SCLEN Account Review Status'
    INTRO = 'Introducing SCLEN.AI - Driving Intelligent Supply Chain Transformation'


class AdminDBColls:
    COMPANY_TYPE = 'company_type'
    USER_TYPE = 'user_type'
    USER_ROLES = 'user_roles'
    PERMISSIONS = 'permissions'
    USER_PERMISSIONS = 'user_permissions'
    CELERY_SCHEDULES = 'celery_schedules'
    SAAS_PRODUCTS = 'saas_products'
    UI_CONFIG = 'ui_config'
    COMPANIES = 'companies'
    UI_MESSAGES = 'ui_messages'
    COMPANY_SETTINGS = 'company_settings'
    FEATURE_SETTINGS = 'feature_settings'
    USERS = 'users'
    USER_SESSIONS = 'user_sessions'
    LOGIN_LOGS = 'login_logs'
    EMAIL_LOGS = 'email_logs'
    EMAIL_CONFIGS = 'email_configs'
    INTRO_EMAIL_RECIPIENTS = 'intro_email_recipients'
    BOUNCED_EMAILS = 'bounced_emails'
    USER_ACTIVITY_LOGS = 'user_activity_logs'
    ACCESS_TOKEN = 'access_token'
    OTP_DETAILS = 'otp_details'
    BLOCKED_USERS = 'blocked_users'
    SMS_VENDORS = 'sms_vendors'
    SMS_SENT_LOGS = 'sms_sent_logs'
    API_REQUEST_LOGS = 'api_request_logs'
    API_KEYS = 'api_keys'
    API_CONFIGS = 'api_configs'
    STATES_LIST = 'states_list'
    APP_URLS = 'app_urls'
    COMPANY_MAPPING = 'company_mapping'
    FEATURE_GROUPS = 'feature_groups'
    FEATURES = 'features'
    PLANS = 'plans'
    SUBSCRIPTIONS = 'subscriptions'
    BILLING_CYCLES = 'billing_cycles'
    CSS_CONFIG = 'css_config'
    TOOLTIPS = 'tooltips'
    DEMO_BOOKINGS = 'demo_bookings'
    CONTRACT_TENURE = 'contract_tenure'
    BUSINESS_SEGMENTS = 'business_segments'
    VEHICLE_BODY_TYPES = 'vehicle_body_types'
    CURRENCY_VS_RATES = 'currency_vs_rates'
    CONTACT_REQUESTS = 'contact_requests'
    BLOGS = 'blogs'
    BLOG_TAGS = 'blog_tags'
    ANALYTICS_MIS_REPORTS = 'analytics_mis_reports'
    ANALYTICS_DASHBOARDS = 'analytics_dashboards'


class CarbonEmissionColls:
    LOAD_TYPES = 'load_types'
    MODE_TYPES = 'mode_types'
    VEHICLE_TYPES = 'vehicle_types'
    EMISSION_FACTORS = 'emission_factors'


class FreightIndexDBColls:
    LOCATION_MASTER = 'location_master'
    FREIGHT_MASTER = 'freight_master'
    FREIGHT_PREDICTIONS = 'freight_predictions'
    DISTANCE_MASTER = 'distance_master'
    SAGEMAKER_MODELS = 'sagemaker_models'
    VEHICLE_MASTER = 'vehicle_master'
    PREDICTED_FREIGHT = 'predicted_freight'


class AccountStatus(Enum):
    IN_REVIEW = 1
    ACTIVE = 2
    REJECTED = 3


class CompanyType(Enum):
    SEEKER = 1
    PROVIDER = 2


class UserType(Enum):
    SEEKER = 1
    PROVIDER = 2
    DRIVER = 3
    CONSIGNEE = 4
    SCLEN = 5


class UserRole(Enum):
    SUPER_ADMIN = 1
    ADMIN_SEEKER = 10
    SEEKER = 20
    PROVIDER = 30


class SAASProduct(Enum):
    NETWORK = 1
    LOGISTICS_PROCUREMENT = 2
    OPTIMIZATION = 3
    EXECUTION = 4
    VISIBILITY = 5
    RECONCILIATION = 6
    ANALYTICS = 7
    ORCHESTRATION = 8
    ILMS = 9


class SAASModules(Enum):
    SUPER_ADMIN = 1
    ADMIN = 2
    PROCUREMENT = 3
    VENDOR_ONBOARDING = 4
    VENDOR_EXPLORER = 5
    FREIGHT_INDEX = 6
    CARBON_EMISSIONS = 7
    ANALYTICS = 8


class SMSVendor:
    BOSCHINDIA = 2


class SubscriptionStatus(Enum):
    PENDING = 1
    ACTIVE = 2
    PAUSED = 3
    EXPIRED = 4
    CANCELLED = 5
    HALTED = 6
    COMPLETED = 7
    TRIAL = 8


class Currency(Enum):
    INR = 'INR'


class PricingPeriod(Enum):
    MONTH = 'month'
    ANNUM = 'annum'


class BillingCycle(Enum):
    # ONE_TIME = 'one_time'
    # DAILY = 'daily'
    # WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    # QUARTERLY = 'quarterly'
    ANNUALLY = 'annually'


class DataType(Enum):
    INT = 'int'
    STR = 'str'
    BOOL = 'bool'


class ValueType(Enum):
    TEXT = 'text'
    NUMBER = 'number'
    PRICE = 'price'
    PRICE_PAY_PER_USE = 'price_pay_per_use'
    DURATION = 'duration'
    TOGGLE = 'toggle'


class CallToAction(Enum):
    REGISTER = 'register'
    TALK_TO_SALES = 'talk_to_sales'


class UsageKeys(Enum):
    NUMBER_OF_LANES = '0c8d39e4fff90959655abfd1fcb3355b'


class BiddingCategory(Enum):
    MONITORED = 1
    UNMONITORED = 2


class VendorBiddingScreen(Enum):
    BLIND_BIDDING = 1
    RANK_INDICATOR = 2
    L1_RATE = 3
    LIVE_STATUS = 4
    L1_AND_LIVE_STATUS = 5


class ContractType(Enum):
    HALF_YEARLY = 1
    MONTHLY = 2
    QUARTERLY = 3
    SPOT = 4
    WEEKLY = 5
    YEARLY = 6
    CUSTOM = 7


class ApiType(Enum):
    GOOGLE_GEOCODE_API = 1
    GOOGLE_PLACES_API = 2
    GOOGLE_DIRECTIONS_API = 3
    GOOGLE_DISTANCE_MATRIX_API = 4
    SEA_RATES_SHIPPING_TYPES = 5
    SEA_RATES_REGIONS = 6
    SEA_RATES_LOCATIONS = 7
    SEA_RATES_FREIGHT_INDEX = 8
    EXCHANGE_RATES = 9
    CUTLY_URL = 10


class MLModelAction(Enum):
    ENCODE = 1
    PREDICT = 2


class FreightIndexModel(Enum):
    LOCATION_BASED = 1
    STATE_BASED = 2


class DemoBookingStatus(Enum):
    PENDING = 1
    CONFIRMED = 2
    COMPLETED = 3
    CANCELLED = 4


class ContactRequestStatus(Enum):
    PENDING = 1
    IN_PROGRESS = 2
    REVIEWED = 3
    RESOLVED = 4


class BlogStatus(Enum):
    DRAFT = 1
    SCHEDULED = 2
    PUBLISHED = 3
    ARCHIVED = 4
