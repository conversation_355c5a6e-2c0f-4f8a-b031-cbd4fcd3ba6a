import logging
from utils import Memoization, fetch_response
from utils.constants import ApiType

logger = logging.getLogger('application')


def get_search_results_from_google_geocode_api(module_id, place_id=None, lat=None, lng=None, address=None):
    api_config = Memoization.get_api_config(ApiType.GOOGLE_GEOCODE_API, module_id)
    api_key = Memoization.get_api_key(module_id)

    params = {
        "key": api_key,
        "language": "en"  # Force results in English, but not 100% effective
    }
    if place_id:
        params["place_id"] = place_id
    elif (lat and lng):
        params["latlng"] = f'{lat},{lng}'
    elif address:
        params["address"] = address
        params["components"] = "country:IN"  # Restrict to India

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    data = response.json()

    status_code = data.get("status", "Unknown status")
    error_message = data.get("error_message", "No results found") if status_code != "OK" else None
    results = []

    for result in data.get("results", []):
        address_components = result.get("address_components", [])

        city = state = state_code = pincode = None
        street_address = result.get("formatted_address", "")

        for component in address_components:
            types = component.get("types", [])

            if "locality" in types:
                city = component.get("long_name")
            elif "administrative_area_level_1" in types:
                state = component.get("long_name")
                state_code = component.get("short_name")
            elif "postal_code" in types:
                pincode = component.get("long_name")

        results.append({
            "address": street_address,
            "pincode": int(pincode) if pincode and pincode.isdigit() else None,
            "city": city,
            "state": state,
            "state_code": state_code,
        })

    return {
        "status": status_code,
        "error_message": error_message,
        "count": len(results),
        "results": results,
        'is_success': bool(results)
    }


def get_search_results_from_google_places_api(module_id, search_term, location_type='establishment'):
    api_config = Memoization.get_api_config(ApiType.GOOGLE_PLACES_API, module_id)
    api_key = Memoization.get_api_key(module_id)

    params = {
        "query": search_term,
        "region": "IN",  # Prioritize India
        "key": api_key,
        "language": "en"  # Force results in English, but not 100% effective
    }
    if location_type:
        # type=establishment parameter works as a bias—it influences the ranking of results
        # but doesn't guarantee that only establishments will be returned
        params['type'] = location_type

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    data = response.json()

    status_code = data.get("status", "Unknown status")
    error_message = data.get("error_message", "No results found") if status_code != "OK" else None
    results = []

    for result in data.get("results", []):
        location = result.get("geometry", {}).get("location", {})

        results.append({
            "place_name": result.get("name"),  # Place name
            "address": result.get("formatted_address"),  # Full address
            "lat": location.get("lat"),
            "lng": location.get("lng"),
            "place_id": result.get("place_id"),  # Google Place ID
            "rating": result.get("rating"),
            "total_ratings": result.get("user_ratings_total")
        })

    return {
        "status": status_code,
        "error_message": error_message,
        "count": len(results),
        "results": results,
        'is_success': bool(results)
    }


def get_google_distance(module_id, origin, destination):
    api_config = Memoization.get_api_config(ApiType.GOOGLE_DISTANCE_MATRIX_API, module_id)
    api_key = Memoization.get_api_key(module_id)

    params = {
        'origins': origin,
        'destinations': destination,
        'key': api_key,
        'units': 'metric'
    }

    response = fetch_response(api_config['api'], params, headers=api_config['headers'], method=api_config['method'])
    response_data = response.json()
    try:
        distance_in_m = response_data['rows'][0]['elements'][0]['distance']['value']
        distance_in_km = round((distance_in_m / 1000.0), 2)
        return distance_in_km
    except Exception:
        return None
