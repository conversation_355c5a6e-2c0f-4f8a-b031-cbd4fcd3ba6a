from django import template

register = template.Library()


@register.filter
def addition(num1, num2):
    return float(num1) + float(num2)


@register.filter
def subtract(num1, num2):
    return round(float(num1) - float(num2), 2)


@register.filter
def format_timestamp(value, str_format='%Y-%m-%dT%H:%M'):
    from utils import DateUtil
    return DateUtil.format_timestamp(value, str_format)


@register.filter
def format_token(value, cleanup=None):
    if cleanup == 'trim':
        return str(int(value))
    return ' - '.join(value.split('-'))


@register.filter
def list_to_string(data, key=None):
    list_data = []
    for item in data:
        if isinstance(item, dict):
            value = str(item.get(key) or '')
        else:
            value = item or ''

        if value and value not in list_data:
            list_data.append(value)
    return ', '.join(list_data)


@register.filter
def format_amount(amount):
    return f"{amount:,.2f}"
