import logging
from celery import shared_task
from django.conf import settings
from config.celery_conf import app  # noqa. Ensure the Celery app is imported
from config.celery_conf import load_dynamic_schedule

logger = logging.getLogger('celery')


# @app.task
@shared_task
def refresh_celery_schedule():
    # app.conf.beat_schedule = load_dynamic_schedule()
    settings.CELERY_BEAT_SCHEDULE = load_dynamic_schedule()
    return "Celery Beat Schedule Updated!"


@shared_task(bind=True)
def add(self, x, y):
    result = x + y
    logger.info(f'add {x} + {y} = {result}')
    return result


@shared_task
def sync_currency_rates():
    from scripts.currency_rates import run
    return run()


@shared_task(bind=True, ignore_result=True)
def send_contact_request_email(self, data):
    from scripts.contact_request_email import run
    return run(data)


@shared_task(bind=True, ignore_result=True)
def send_demo_booking_email(self, data):
    from scripts.book_demo_email import run
    return run(data)


@shared_task(bind=True, ignore_result=True)
def send_account_review_email(self, data):
    from scripts.account_reviewed_email import run
    return run(data)
