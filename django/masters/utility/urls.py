from django.urls import path
from .views import (
    DropdownsAPIView,
    ListMappedSeekersAPI,
    SearchAddressAPI,
    CurrencyRatesApi,
    EmailTrackingPixel,
    MISToken,
    GetMISDashboardLink
)

urlpatterns = [
    path('dropdowns/', DropdownsAPIView.as_view(), name='dropdowns'),
    path('provider/companies/listing/', ListMappedSeekersAPI.as_view(), name='list_mapped_seekers'),
    path('search-address/', SearchAddressAPI.as_view(), name='search_address'),
    path('currency-rates/', CurrencyRatesApi.as_view(), name='currency_rates'),
    path('email/read-receipt/', EmailTrackingPixel.as_view(), name='email_read_receipt_api'),
    path('mis-token/', MISToken.as_view(), name="mis_token"),
    path('mis-dashboard-link/', GetMISDashboardLink.as_view(), name="mis_dashboard_link"),
]
