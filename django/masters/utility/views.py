import logging
import base64
from rest_framework import status
from rest_framework.views import APIView
from django.http import HttpResponse
from throttle import CustomRateThrottle
from utils import (
    get_search_results_from_google_geocode_api,
    get_search_results_from_google_places_api,
    make_currency_vs_rates,
    format_error_response,
    get_currency_rates,
    format_response,
    Memoization,
    DateUtil
)
from utils.constants import (
    AdminDBColls,
    SAASModules,
    ApiType,
)
from utils.mongo import MongoUtility
from authn import (
    AuthenticateSeekerAdmin,
    AuthenticateProvider
)

logger = logging.getLogger('application')

# 1x1 transparent GIF
TRANSPARENT_PIXEL = base64.b64decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')


class EmailTrackingPixel(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'track_email'

    def get(self, request, *args, **kwargs):
        # tracking info
        log_id = request.query_params.get('log_id')
        logger.info(f"Email opened: log_id={log_id}")

        if log_id:
            db = MongoUtility()
            now = DateUtil.get_current_timestamp()
            db.update(AdminDBColls.EMAIL_LOGS, {'id': log_id}, {'is_read': True, 'updated_on': now})

        return HttpResponse(TRANSPARENT_PIXEL, content_type='image/gif', status=status.HTTP_200_OK)


class DropdownsAPIView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        params = request.GET

        try:
            dropdown_types = params.get('dropdown_type').split(',')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid dropdown_type recieved.',
            )

        data = {}
        for dropdown_type in dropdown_types:
            if hasattr(self, f'get_{dropdown_type}_dropdown'):
                dropdown_func = getattr(self, f'get_{dropdown_type}_dropdown')
            else:
                dropdown_func = self.get_dropdown_data

            data[dropdown_type] = dropdown_func(dropdown_type)

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_dropdown_data(self, dropdown_type):
        dropdown_coll = dropdown_type
        return Memoization.get_dropdown_data(dropdown_coll)


class ListMappedSeekersAPI(APIView):
    authentication_classes = (AuthenticateProvider, )

    def get(self, request):
        provider_id = request.company_id

        db = MongoUtility()

        df = {'_id': 0, 'seeker_id': 1, 'seeker_name': 1}

        # Get mappings with pagination
        mappings = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': provider_id}, df, sort=[('seeker_name', 1)])

        # Convert cursor to list
        seeker_mappings = [x for x in mappings]

        # Prepare response data
        response_data = {
            'seekers': seeker_mappings,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class SearchAddressAPI(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        params = request.GET
        search_term = params.get('search_term')
        lat = params.get('lat')
        lng = params.get('lng')
        place_id = params.get('place_id')
        location_type = params.get('type')

        try:
            module_id = int(params.get('module_id'))
            if module_id not in [SAASModules.CARBON_EMISSIONS.value, SAASModules.FREIGHT_INDEX.value]:
                raise ValueError
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid module_id recieved.')

        try:
            api_type = int(params.get('api_type', ApiType.GOOGLE_PLACES_API.value))
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid api_type recieved.')

        try:
            if api_type == ApiType.GOOGLE_PLACES_API.value:
                results = get_search_results_from_google_places_api(module_id, search_term, location_type)
            elif api_type == ApiType.GOOGLE_GEOCODE_API.value:
                results = get_search_results_from_google_geocode_api(module_id, place_id, lat, lng, search_term)
            else:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid api_type recieved.')
        except Exception as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        return format_response(status.HTTP_200_OK, results, 'Success')


class CurrencyRatesApi(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        db = MongoUtility()
        now = DateUtil.get_current_timestamp()
        created_on = DateUtil.convert_to_datetime(now)
        created_on = DateUtil.format_timestamp(created_on, str_format='%Y%m%d')
        created_on = int(created_on)
        query = {'created_on': created_on}
        currency_vs_rates = db.find(AdminDBColls.CURRENCY_VS_RATES, query, find_one=True)

        if not currency_vs_rates:
            response, success = get_currency_rates()
            if not success:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    'Exchange rates not available at the moment, please try again later'
                )

            currency_vs_rates = make_currency_vs_rates(response['quotes'], created_on)
            db.insert(AdminDBColls.CURRENCY_VS_RATES, [currency_vs_rates])
            currency_vs_rates.pop('_id', None)

        return format_response(
            status.HTTP_200_OK,
            currency_vs_rates,
            'Exchange rates retrieved successfully'
        )
