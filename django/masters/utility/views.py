import jwt
import time
import logging
import base64
import hashlib
import hmac
from rest_framework import status
from rest_framework.views import APIView
from django.http import HttpResponse
from throttle import CustomRateThrottle
from utils import (
    get_search_results_from_google_geocode_api,
    get_search_results_from_google_places_api,
    make_currency_vs_rates,
    format_error_response,
    get_currency_rates,
    format_response,
    Memoization,
    DateUtil
)
from utils.constants import (
    AdminDBColls,
    SAASModules,
    ApiType,
)
from utils.mongo import MongoUtility
from authn import (
    AuthenticateSeeker,
    AuthenticateSeekerAdmin,
    AuthenticateProvider,
    AuthenticateAll
)

logger = logging.getLogger('application')

# 1x1 transparent GIF
TRANSPARENT_PIXEL = base64.b64decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')


class EmailTrackingPixel(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'track_email'

    def get(self, request, *args, **kwargs):
        # tracking info
        log_id = request.query_params.get('log_id')
        logger.info(f"Email opened: log_id={log_id}")

        if log_id:
            db = MongoUtility()
            now = DateUtil.get_current_timestamp()
            db.update(AdminDBColls.EMAIL_LOGS, {'id': log_id}, {'is_read': True, 'updated_on': now})

        return HttpResponse(TRANSPARENT_PIXEL, content_type='image/gif', status=status.HTTP_200_OK)


class DropdownsAPIView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        params = request.GET

        try:
            product_id = int(params.get('product_id'))
        except (TypeError, ValueError):
            product_id = None

        try:
            dropdown_types = params.get('dropdown_type').split(',')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid dropdown_type recieved.',
            )

        data = {}
        for dropdown_type in dropdown_types:
            if hasattr(self, f'get_{dropdown_type}_dropdown'):
                dropdown_func = getattr(self, f'get_{dropdown_type}_dropdown')
            else:
                dropdown_func = self.get_dropdown_data

            data[dropdown_type] = dropdown_func(dropdown_type, product_id)

        return format_response(status.HTTP_200_OK, data, 'Success')

    def get_dropdown_data(self, dropdown_type, product_id=None):
        dropdown_coll = dropdown_type
        return Memoization.get_dropdown_data(dropdown_coll, product_id=product_id)


class ListMappedSeekersAPI(APIView):
    authentication_classes = (AuthenticateProvider, )

    def get(self, request):
        provider_id = request.company_id

        db = MongoUtility()

        df = {'_id': 0, 'seeker_id': 1, 'seeker_name': 1}

        # Get mappings with pagination
        mappings = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': provider_id}, df, sort=[('seeker_name', 1)])

        # Convert cursor to list
        seeker_mappings = [x for x in mappings]

        # Prepare response data
        response_data = {
            'seekers': seeker_mappings,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class SearchAddressAPI(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        params = request.GET
        search_term = params.get('search_term')
        lat = params.get('lat')
        lng = params.get('lng')
        place_id = params.get('place_id')
        location_type = params.get('type')

        try:
            module_id = int(params.get('module_id'))
            if module_id not in [SAASModules.CARBON_EMISSIONS.value, SAASModules.FREIGHT_INDEX.value]:
                raise ValueError
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid module_id recieved.')

        try:
            api_type = int(params.get('api_type', ApiType.GOOGLE_PLACES_API.value))
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid api_type recieved.')

        try:
            if api_type == ApiType.GOOGLE_PLACES_API.value:
                results = get_search_results_from_google_places_api(module_id, search_term, location_type)
            elif api_type == ApiType.GOOGLE_GEOCODE_API.value:
                results = get_search_results_from_google_geocode_api(module_id, place_id, lat, lng, search_term)
            else:
                return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid api_type recieved.')
        except Exception as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))

        return format_response(status.HTTP_200_OK, results, 'Success')


class CurrencyRatesApi(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        db = MongoUtility()
        now = DateUtil.get_current_timestamp()
        created_on = DateUtil.convert_to_datetime(now)
        created_on = DateUtil.format_timestamp(created_on, str_format='%Y%m%d')
        created_on = int(created_on)
        query = {'created_on': created_on}
        currency_vs_rates = db.find(AdminDBColls.CURRENCY_VS_RATES, query, find_one=True)

        if not currency_vs_rates:
            response, success = get_currency_rates()
            if not success:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    'Exchange rates not available at the moment, please try again later'
                )

            currency_vs_rates = make_currency_vs_rates(response['quotes'], created_on)
            db.insert(AdminDBColls.CURRENCY_VS_RATES, [currency_vs_rates])
            currency_vs_rates.pop('_id', None)

        return format_response(
            status.HTTP_200_OK,
            currency_vs_rates,
            'Exchange rates retrieved successfully'
        )


class MISToken(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request):
        embed_code = request.GET.get('embed_code')

        if not embed_code:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid embed code.')

        db = MongoUtility()
        obj = db.find(AdminDBColls.ANALYTICS_MIS_REPORTS, {'embed_code': embed_code}, find_one=True)
        if not obj:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Data not found for given embed code.')

        # Will expire after 1 day
        expiry = int(time.time()) + 24 * 60
        payload = {
            'exp': expiry,
            'company_id': request.company_id,
        }
        token = jwt.encode(payload, str(obj['secret_key']), 'HS256')

        response_data = {
            'embed_code': embed_code,
            'token': token,
            'expiry': expiry,
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class GetMISDashboardLink(APIView):
    authentication_classes = (AuthenticateSeeker, )

    def get(self, request):
        embed_code = request.GET.get('embed_code')

        if not embed_code:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid embed code.')

        db = MongoUtility()
        obj = db.find(AdminDBColls.ANALYTICS_DASHBOARDS, {'embed_code': embed_code}, find_one=True)
        if not obj:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Data not found for given embed code.')

        response_data = {
            'embed_code': embed_code,
            'signed_url': self.create_embed_url(obj['embed_path'], request.company_id, obj['secret_key']),
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def create_embed_url(self, embed_path, company_id, secret):
        nonce = base64.urlsafe_b64encode(hashlib.sha256(str(time.time()).encode()).digest())[:16].decode()
        params = {
            "nonce": nonce,
            "time": int(time.time()),
            "company_id": company_id,
            "embed_path": embed_path,
        }
        string_to_sign = f"{params['embed_path']}\n{params['nonce']}\n{params['time']}\n{params['company_id']}"
        signature = base64.urlsafe_b64encode(hmac.new(secret.encode(), string_to_sign.encode(), hashlib.sha1).digest()).decode()

        signed_url = f"{params['embed_path']}?nonce={params['nonce']}&time={params['time']}&company_id={params['company_id']}&signature={signature}"
        return signed_url
