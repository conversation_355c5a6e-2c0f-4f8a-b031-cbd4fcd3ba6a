from typing import Literal
from pydantic import (
    BaseModel, Field
)
from utils import DateUtil
from utils.constants import (
    SAASProduct,
    BiddingCategory,
    VendorBiddingScreen
)


class ProcurementSettingsPayloadValidator(BaseModel):
    product_id: Literal[SAASProduct.LOGISTICS_PROCUREMENT.value] = SAASProduct.LOGISTICS_PROCUREMENT.value
    enable_tat_visibility: bool = False
    bidding_category: str = BiddingCategory.MONITORED.name.capitalize()
    bidding_category_id: BiddingCategory = BiddingCategory.MONITORED.value
    bid_decremental: int = Field(default=0, ge=0)
    bid_tolerance: int | None = Field(default=None, ge=0, le=100)
    l1_rate_decremental: int = Field(default=0, ge=0)
    vendor_bidding_screen: str = VendorBiddingScreen.BLIND_BIDDING.name.replace('_', ' ').capitalize()
    vendor_bidding_screen_id: VendorBiddingScreen = VendorBiddingScreen.BLIND_BIDDING.value
    enable_chp_with_cop: bool = False
    min_bidders_for_loi: int = Field(default=0, ge=0)
    enable_loi_approval: bool = False
    auto_send_loi_to_l1: bool = False
    enable_sms: bool = False
    enable_email: bool = False
    enable_whatsapp: bool = False
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values
