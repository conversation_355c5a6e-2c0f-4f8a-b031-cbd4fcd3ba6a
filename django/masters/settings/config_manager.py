class BaseSetting:
    def __init__(self, key, label, tooltip=None, default=None):
        self.key = key
        self.label = label
        self.tooltip = tooltip
        self.default = default

    def to_dict(self):
        raise NotImplementedError("Each setting must implement `to_dict()`.")


class BooleanSetting(BaseSetting):
    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "boolean",
            "tooltip": self.tooltip,
            "value": self.default
        }


class NumberSetting(BaseSetting):
    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "number",
            "tooltip": self.tooltip,
            "value": self.default
        }


class TextSetting(BaseSetting):
    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "text",
            "tooltip": self.tooltip,
            "value": self.default
        }


class DropdownSetting(BaseSetting):
    def __init__(self, key, label, options, tooltip=None, default=None):
        super().__init__(key, label, tooltip, default)
        self.options = options

    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "dropdown",
            "tooltip": self.tooltip,
            "options": self.options,
            "value": self.default
        }


class BooleanWithValueSetting(BaseSetting):
    def __init__(self, key, label, value_label, tooltip=None, default_enabled=False, default_value=None, value_type="number"):
        super().__init__(key, label, tooltip, default_enabled)
        self.value_label = value_label
        self.value_type = value_type
        self.default_value = default_value

    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "boolean_with_value",
            "tooltip": self.tooltip,
            "enabled": self.default,
            "value": {
                "label": self.value_label,
                "type": self.value_type,
                "value": self.default_value
            }
        }


class BooleanWithDropdownSetting(BaseSetting):
    def __init__(self, key, label, dropdown_label, options, tooltip=None, default_enabled=False, default_value=None):
        super().__init__(key, label, tooltip, default_enabled)
        self.dropdown_label = dropdown_label
        self.options = options
        self.default_value = default_value

    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "boolean_with_dropdown",
            "tooltip": self.tooltip,
            "enabled": self.default,
            "value": {
                "label": self.dropdown_label,
                "type": "dropdown",
                "options": self.options,
                "value": self.default_value
            }
        }


class GroupSetting(BaseSetting):
    def __init__(self, key, label, fields, tooltip=None, default=None):
        super().__init__(key, label, tooltip, default)
        self.fields = fields

    def to_dict(self):
        return {
            "key": self.key,
            "label": self.label,
            "type": "group",
            "tooltip": self.tooltip,
            "fields": [field.to_dict() for field in self.fields],
            "value": self.default
        }
