import logging
from pydantic import ValidationError
from rest_framework import status
from rest_framework.views import APIView
from utils import (
    format_response,
    format_error_response,
    Memoization
)
from utils.constants import (
    AdminDBColls, SubscriptionStatus,
    SAASProduct
)
from utils.mongo import MongoUtility
from authn import AuthenticateSeekerAdmin
from .request_validators import ProcurementSettingsPayloadValidator

logger = logging.getLogger('application')


class FeatureSettingsAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        params = request.GET
        try:
            product_id = int(params.get('product_id'))
        except (TypeError, ValueError):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Invalid product_id')

        response_data = {'groups': [], 'settings': {}}

        db = MongoUtility()

        query = {
            'company_id': request.company_id,
            'product_id': product_id,
        }
        product_settings = db.find(AdminDBColls.FEATURE_SETTINGS, query, find_one=True)
        if not product_settings:
            product_settings = ProcurementSettingsPayloadValidator().model_dump()

        response_data['settings'] = product_settings

        query['status_id'] = {'$in': [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]}

        subscription = db.find(AdminDBColls.SUBSCRIPTIONS, query, {'plan_id': 1}, find_one=True)
        if not subscription:
            return format_response(status.HTTP_200_OK, response_data, 'Success')

        plan = db.find(AdminDBColls.PLANS, {'id': subscription['plan_id']}, find_one=True)
        tooltips = Memoization.get_tooltips(product_id)

        groups_map = {}
        for feature in plan.get('features', []):
            if not feature['is_cfg']:
                continue

            fid, fname = feature['id'], feature['name']
            gid, gname = feature['gid'], feature['gname']

            config = {
                'id': fid,
                'name': fname,
                'is_available': feature['ival'],
                'tooltip': tooltips.get(fid)
            }
            if gid not in groups_map:
                groups_map[gid] = {
                    'gid': gid,
                    'gname': gname,
                    'features': [config]
                }
            else:
                groups_map[gid]['features'].append(config)

        response_data['groups'] = sorted(groups_map.values(), key=lambda d: d['gname'])
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def post(self, request, *args, **kwargs):
        payload = request.data

        if payload.get('product_id') == SAASProduct.LOGISTICS_PROCUREMENT.value:
            try:
                validated_data = ProcurementSettingsPayloadValidator(**payload).model_dump()
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)
        else:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Settings not implemented.')

        update_query = {
            **validated_data,
            'company_id': request.company_id,
        }

        db = MongoUtility()

        query = {
            'company_id': request.company_id,
            'product_id': payload['product_id']
        }
        db.update(AdminDBColls.FEATURE_SETTINGS, query, update_query, upsert=True)

        return format_response(status.HTTP_200_OK, {}, 'Success')
