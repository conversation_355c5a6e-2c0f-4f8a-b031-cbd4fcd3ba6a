import logging
from rest_framework import status
from rest_framework.views import APIView
from utils import (
    format_response,
    format_error_response,
)
from utils.constants import AdminDBColls
from utils.mongo import MongoUtility
from subscription import SubscriptionService
from authn import AuthenticateAll

logger = logging.getLogger('application')


class Components(object):
    SIDEBAR = 'sidebar'
    ADMIN = 'admin'
    ADMIN_SETTINGS = 'admin_settings'
    PROCUREMENT = 'procurement'
    VENDOR_ONBOARDING = 'vendor_onboarding'


class UIConfig(APIView):
    authentication_classes = (AuthenticateAll, )

    def get(self, request):
        params = request.GET
        self.request = request

        component = params.get('component')

        try:
            config_func = getattr(self, f'get_{component}_config')
        except AttributeError:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Invalid component value recieved.',
            )

        self.db = MongoUtility()

        data = config_func(component)
        return format_response(
            status.HTTP_200_OK,
            data,
            'Success'
        )

    def get_data_from_db(self, component, coll=AdminDBColls.UI_CONFIG, find_one=True, sort=None):
        query = {
            'component': component,
            'is_active': True
        }
        return self.db.find(coll, query, find_one=find_one, sort=sort or [])

    def get_sidebar_config(self, component):
        """
        Get sidebar configuration with dynamic enabling/disabling based on subscription status.

        The sidebar options can have a 'subscription_type' field with the following values:
        - 'product_specific': Enabled only if the company has an active subscription for the specified product_id
        - 'any_subscription': Enabled if the company has at least one active subscription of any type
        - 'none' or None: Always enabled by default (if is_enabled is True in the database)

        Args:
            component (str): The component name

        Returns:
            list: List of sidebar options with is_enabled flag set based on subscription status
        """
        objs = self.get_data_from_db(component, find_one=False, sort=[('order', 1)])
        company_id = self.request.company_id

        # Get active subscriptions for the company
        active_product_ids = self._get_active_product_ids(company_id)
        has_any_subscription = len(active_product_ids) > 0

        sidebar_options = []
        for obj in objs:
            if obj['permission'] not in self.request.user_permissions:
                continue

            # Check if this sidebar option is tied to a product
            product_id = obj.get('product_id')
            is_enabled = obj['is_enabled']

            # Check subscription requirements
            subscription_type = obj.get('subscription_type', None)

            # Handle different subscription types
            if subscription_type == 'product_specific' and product_id is not None:
                # Enable only if the company has an active subscription for this specific product
                is_enabled = is_enabled and (product_id in active_product_ids)
            elif subscription_type == 'any_subscription':
                # Enable if the company has at least one active subscription of any type
                is_enabled = is_enabled and has_any_subscription

            option = {
                'id': obj['id'],
                'component_type': obj['component_type'],
                'name': obj['name'],
                'is_enabled': is_enabled,
            }

            child_list = []
            for child_obj in obj.get('child_list', []):
                if not child_obj['is_active']:
                    continue

                if child_obj['permission'] not in self.request.user_permissions:
                    continue

                # Check if this child option is tied to a product
                child_product_id = child_obj.get('product_id')
                child_is_enabled = child_obj['is_enabled']

                # Check subscription requirements for child option
                child_subscription_type = child_obj.get('subscription_type', 'none')

                # Handle different subscription types for child options
                if child_subscription_type == 'product_specific' and child_product_id is not None:
                    # Enable only if the company has an active subscription for this specific product
                    child_is_enabled = child_is_enabled and (child_product_id in active_product_ids)
                elif child_subscription_type == 'any_subscription':
                    # Enable if the company has at least one active subscription of any type
                    child_is_enabled = child_is_enabled and has_any_subscription

                child_option = {
                    'id': child_obj['id'],
                    'component_type': child_obj['component_type'],
                    'name': child_obj['name'],
                    'is_enabled': child_is_enabled,
                }

                child_list.append(child_option)

            if child_list:
                option['child_list'] = child_list

            sidebar_options.append(option)
        return sidebar_options

    def _get_active_product_ids(self, company_id):
        """
        Get a list of product IDs for which the company has active subscriptions.
        Uses the SubscriptionService class to get subscription data from cache.

        Args:
            company_id (str): The company ID

        Returns:
            list: List of product IDs with active subscriptions
        """
        # Get subscription data from cache
        subscription_data = SubscriptionService.get_company_subscriptions(company_id)

        # Get product IDs with active subscriptions
        active_product_ids = []

        # Check each product subscription to see if it's active
        for product_id in subscription_data.get('product_ids', []):
            # Check if the subscription is active
            status_info = SubscriptionService.is_product_subscription_active(company_id, product_id)
            if status_info.get('is_active', False):
                active_product_ids.append(product_id)

        return active_product_ids

    def get_default_config(self, component):
        data = {'tabs': []}
        config = self.get_data_from_db(component)
        if config['permission'] not in self.request.user_permissions:
            return data

        data['tabs'] = [{'id': x['id'], 'name': x['name']} for x in config.get('tabs', []) if x['is_active']]
        return data

    def get_procurement_config(self, component):
        return self.get_default_config(component)

    def get_vendor_onboarding_config(self, component):
        return self.get_default_config(component)

    def get_admin_settings_config(self, component):
        """
        Get admin settings configuration based on active subscriptions.
        Uses the SubscriptionService class to get subscription data from cache.

        Args:
            component (str): The component name

        Returns:
            dict: Admin settings configuration with tabs for active subscriptions
        """
        company_id = self.request.company_id

        # Get subscription data from cache
        subscription_data = SubscriptionService.get_company_subscriptions(company_id)

        # Get active subscriptions
        active_subscriptions = []
        for product_id in subscription_data.get('product_ids', []):
            # Check if the subscription is active
            status_info = SubscriptionService.is_product_subscription_active(company_id, product_id)
            if status_info.get('is_active', False):
                # Get the subscription details
                subscription = subscription_data.get('products', {}).get(product_id, {})
                if subscription:
                    active_subscriptions.append({
                        'product_id': product_id,
                        'product_name': subscription.get('product_name', f'Product {product_id}')
                    })

        # Create tabs for active subscriptions
        data = {
            'tabs': [{'id': sub['product_id'], 'name': sub['product_name']} for sub in active_subscriptions]
        }
        return data
