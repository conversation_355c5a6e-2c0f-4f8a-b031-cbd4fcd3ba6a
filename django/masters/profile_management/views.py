import logging
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from utils import (
    format_error_response,
    format_response,
)
from utils.constants import (
    UserRole,
    AdminDBColls,
    ErrorMessages,
)
from authn.decorators import validate_request_payload
from utils.mongo import MongoUtility
from authn import AuthenticateAll
from .request_validators import UpdateProfilePayloadValidator

logger = logging.getLogger('application')


@method_decorator(validate_request_payload(UpdateProfilePayloadValidator), name='put')
class UserRetrieveUpdateAPI(APIView):
    authentication_classes = (AuthenticateAll, )

    def get(self, request, *args, **kwargs):
        user_id = kwargs['user_id']

        if user_id != request.user_id:
            return format_error_response(status.HTTP_403_FORBIDDEN, ErrorMessages.ACCESS_DENIED)

        db = MongoUtility()

        data_filter = {
            '_id': 0,
            'pwd_hash': 0,
            'pwd_changed_on': 0,
            'pwd_history': 0,
        }

        user_details = db.find(AdminDBColls.USERS, {'id': user_id}, data_filter, find_one=True)
        if not user_details:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        company_details = {}
        if user_details['user_role'] == UserRole.ADMIN_SEEKER.value:
            company_details = db.find(AdminDBColls.COMPANIES, {'id': request.company_id}, find_one=True)

        ui_messages = {}
        ui_messages_doc = db.find(AdminDBColls.UI_MESSAGES, {'type': 'profile'}, find_one=True)
        if not (user_details['email_verified_on'] and company_details['email_verified_on']):
            ui_messages['unverified_email'] = ui_messages_doc.get('unverified_email')

        if not company_details.get('has_subscribed'):
            acc_status = company_details.get('acc_status').lower()
            ui_messages[acc_status] = ui_messages_doc.get(acc_status)

        response_data = {
            'user_details': user_details,
            'company_details': company_details,
            'ui_messages': ui_messages
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')

    def put(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        user_data = request.validated_payload.model_dump()

        if user_id != request.user_id:
            return format_error_response(status.HTTP_403_FORBIDDEN, ErrorMessages.ACCESS_DENIED)

        db = MongoUtility()

        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)
        if not user:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        db.update(AdminDBColls.USERS, {'id': user_id}, user_data)
        return format_response(status.HTTP_200_OK, {}, 'Success')
