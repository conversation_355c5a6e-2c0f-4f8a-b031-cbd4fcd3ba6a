from django.urls import path
from .views import (
    UserCRUDAPI,
    ResendInviteAPI,
    ListUsersApi,
    ProviderGroupCRUDAPI
)


urlpatterns = [
    path('users/listing', ListUsersApi.as_view(), name='list_users'),
    path('user/create', UserCRUDAPI.as_view(), name='create_user'),
    path('user/<str:user_id>', UserCRUDAPI.as_view(), name='update_delete_user'),
    path('user/<str:user_id>/resend-invite', ResendInviteAPI.as_view(), name='resend_invite'),
    path('provider-groups/create/', ProviderGroupCRUDAPI.as_view(), name='create_provider_group'),
    path('provider-groups/<str:group_id>/', ProviderGroupCRUDAPI.as_view(), name='update_delete_provider_group'),
]
