import logging
from utils import send_email, J<PERSON><PERSON>oken, Memoization
from utils.constants import (
    EmailHeader,
    AppDomainID,
    AppPathID
)

logger = logging.getLogger('application')


def send_new_user_invite(user_data: dict, admin_user_data: dict) -> None:
    email_template = 'new_user_invite.html'

    user_email = user_data['email']
    payload = {'email': user_email}
    jwt_token = JWTToken.get_token(payload, user_data['id'], JWTToken.Purpose.ACTIVATE_USER, JWTToken.Expiry.USER_INVITE)
    activation_link = f"{Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_ACTIVATE_ACCOUNT)['url']}?token={jwt_token}"

    message = {
        'user_name': user_data['user_name'],
        'admin_user': {
            'user_name': admin_user_data['user_name'],
            'company_name': admin_user_data['company_name'],
        },
        'activation_link': activation_link,
    }
    send_email([user_email], cc=[], bcc=[], subject=EmailHeader.USER_INVITE.value, message=message, template=email_template)


def send_old_user_invite(user_data: dict, admin_user_data: dict) -> None:
    email_template = 'old_provider_invite.html'

    message = {
        'user_name': user_data['user_name'],
        'admin_user': {
            'user_name': admin_user_data['user_name'],
            'company_name': admin_user_data['company_name'],
        },
        'login_link': Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_LOGIN)['url'],
    }
    send_email([user_data['email']], cc=[], bcc=[], subject=EmailHeader.NOTIFICATION.value, message=message, template=email_template)
