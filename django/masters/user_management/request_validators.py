from typing_extensions import Self
from pydantic import (
    BaseModel, EmailStr, Field,
    field_validator, model_validator
)
from utils import is_valid_domain, DateUtil
from utils.constants import CompanyType


class NewUserPayloadValidator(BaseModel):
    company_type: CompanyType
    company_name: str = Field(default=None, min_length=3)  # Optional by default
    user_name: str = Field(..., min_length=3)
    email: EmailStr
    phone: int

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @field_validator('email', mode='after')
    def validate_email(cls, value: str) -> str:
        if not is_valid_domain(value):
            raise ValueError("Invalid email domain.")
        return value.lower()

    @field_validator('phone', mode='after')
    def validate_phone(cls, value: int) -> int:
        if len(str(value)) != 10:
            raise ValueError("Invalid Phone number. It must be a 10-digit number.")
        return value

    @model_validator(mode="after")
    def validate_company_name(self) -> Self:
        # Validate company_name based on company_type
        if self.company_type != CompanyType.SEEKER.value and not self.company_name:
            raise ValueError("Company name is required.")
        return self


class UpdateUserPayloadValidator(BaseModel):
    user_name: str = Field(..., min_length=3)
    phone: int
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    @field_validator('phone', mode='after')
    def validate_phone(cls, value: int) -> int:
        if len(str(value)) != 10:
            raise ValueError("Invalid Phone number. It must be a 10-digit number.")
        return value


class ListUsersParamsValidator(BaseModel):
    company_type: int = Field(..., description="get params are always strings and enum type fields aren't typecasted", in_=[ct.value for ct in CompanyType])
    limit: int = Field(50, ge=1, le=100)
    offset: int = 0
    sort_by: str = '_id'
    sort_order: int = Field(default=1, description="Sort order must be either 1 (ascending) or -1 (descending)", in_=[1, -1])
