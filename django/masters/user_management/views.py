import logging
from pydantic import ValidationError
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from throttle import CustomRateThrottle
from utils import (
    format_error_response,
    format_response,
)
from utils.constants import (
    CompanyType,
    UserType,
    UserRole,
    AdminDBColls,
)
from authn.decorators import validate_query_params, validate_request_payload
from utils.mongo import MongoUtility
from schema import UserSchema, ProviderCompanySchema, CompanyMappingSchema
from authn import AuthenticateSeekerAdmin
from .request_validators import (
    NewUserPayloadValidator,
    UpdateUserPayloadValidator,
    ListUsersParamsValidator
)
from .utils import send_new_user_invite, send_old_user_invite

logger = logging.getLogger('application')


@method_decorator(validate_request_payload(NewUserPayloadValidator), name='post')
@method_decorator(validate_request_payload(UpdateUserPayloadValidator), name='put')
class UserCRUDAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request):
        user_data = request.validated_payload.model_dump()

        self.db = MongoUtility()

        admin_user_data = {
            'user_name': request.user_name,
            'company_name': request.company_name,
            'company_id': request.company_id
        }

        if user_data['company_type'] == CompanyType.SEEKER.value:
            # process seeker user data
            try:
                user_doc = self.create_seeker_user(user_data, admin_user_data)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            send_new_user_invite(user_doc, admin_user_data)
        else:
            # process provider user data
            try:
                is_created, user_doc = self.create_or_map_provider_user(user_data, admin_user_data)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            if is_created:
                send_new_user_invite(user_doc, admin_user_data)
            else:
                send_old_user_invite(user_doc, admin_user_data)

        return format_response(status.HTTP_200_OK, {}, 'Success')

    def create_seeker_user(self, user_data, admin_user_data):
        self.check_if_user_exists(user_data)

        user_data.update({
            'company_id': admin_user_data['company_id'],
            'company_name': admin_user_data['company_name'],
            'user_type': UserType.SEEKER.value,
            'user_role': UserRole.SEEKER.value,
        })

        user_doc = UserSchema(**user_data).model_dump()
        self.db.insert(AdminDBColls.USERS, [user_doc])
        return user_doc

    def create_or_map_provider_user(self, user_data, admin_user_data):
        existing_provider = self.check_if_user_exists(user_data)
        is_created = False

        if not existing_provider:
            company_doc = ProviderCompanySchema(name=user_data['company_name'], email=user_data['email'], phone=user_data['phone']).model_dump()

            user_data.update({
                'company_id': company_doc['id'],
                'user_type': UserType.PROVIDER.value,
                'user_role': UserRole.PROVIDER.value,
            })
            user_doc = UserSchema(**user_data).model_dump()

            self.db.insert(AdminDBColls.COMPANIES, [company_doc])
            self.db.insert(AdminDBColls.USERS, [user_doc])
            is_created = True
        else:
            self.check_if_provider_mapped(existing_provider['company_id'], admin_user_data['company_id'])
            user_doc = existing_provider

        mapping_doc = {
            'seeker_id': admin_user_data['company_id'],
            'seeker_name': admin_user_data['company_name'],
            'provider_id': user_doc['company_id'],
            'provider_name': user_doc['company_name']
        }

        mapping_doc = CompanyMappingSchema(**mapping_doc).model_dump()
        self.db.insert(AdminDBColls.COMPANY_MAPPING, [mapping_doc])
        return is_created, user_doc

    def check_if_user_exists(self, user_data):
        # check is user with same phone number exists
        user = self.db.find(AdminDBColls.USERS, {'phone': user_data['phone']}, find_one=True)
        if user:
            raise ValueError(f'User with phone: `{user["phone"]}` already exists.')

        # check is user with same email exists
        user = self.db.find(AdminDBColls.USERS, {'email': user_data['email']}, find_one=True)
        if user:
            if (user['company_type'] == CompanyType.PROVIDER.value) and (user_data['company_type'] == CompanyType.PROVIDER.value):
                return user
            else:
                # if user already exists as a seeker or
                # if user already exists as a provider but admin is trying to add the same user as seeker
                raise ValueError(f'User with email: `{user["email"]}` already exists.')
        return False

    def check_if_provider_mapped(self, provider_id, seeker_id):
        mapped_doc = self.db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': provider_id, 'seeker_id': seeker_id}, find_one=True)
        if mapped_doc:
            raise ValueError(f'Provider already mapped.')
        return False

    def put(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id
        user_data = request.validated_payload.model_dump()

        db = MongoUtility()
        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        can_update_user = True
        if user['company_type'] == CompanyType.PROVIDER.value:
            company_mapping = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id}, find_one=True)
            can_update_user = bool(company_mapping)
        elif user['company_id'] != admin_company_id:
            can_update_user = False

        if can_update_user:
            db.update(AdminDBColls.USERS, {'id': user_id}, user_data)
            return format_response(status.HTTP_200_OK, {}, 'Success')

        return format_response(status.HTTP_400_BAD_REQUEST, 'User not mapped.')

    def delete(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id

        db = MongoUtility()
        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        if user['company_type'] == CompanyType.PROVIDER.value:
            db.delete(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id})
        elif user['company_id'] == admin_company_id:
            db.delete(AdminDBColls.USERS, {'id': user_id})

        return format_response(status.HTTP_200_OK, {}, 'Success')


@method_decorator(validate_query_params(ListUsersParamsValidator), name='get')
class ListUsersApi(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request):
        validated_params = request.validated_params

        company_type = validated_params.company_type
        limit = validated_params.limit
        offset = validated_params.offset
        sort_by = validated_params.sort_by
        sort_order = validated_params.sort_order

        seeker_id = request.company_id
        sort_list = [(sort_by, sort_order)]

        db = MongoUtility()

        data_filter = {
            '_id': 0,
            'pwd_hash': 0,
            'pwd_changed_on': 0,
            'pwd_history': 0,
        }

        if company_type == CompanyType.SEEKER.value:
            filter_query = {'company_id': seeker_id, 'company_type': CompanyType.SEEKER.value}
            users = [x for x in db.find(AdminDBColls.USERS, filter_query, data_filter, sort=sort_list).limit(limit).skip(offset)]
        else:
            query = {'seeker_id': seeker_id}
            df = {'_id': 0, 'provider_id': 1}
            mapped_providers = [x['provider_id'] for x in db.find(AdminDBColls.COMPANY_MAPPING, query, df, sort=sort_list).limit(limit).skip(offset)]

            filter_query = {'company_id': {'$in': mapped_providers}, 'company_type': CompanyType.PROVIDER.value}
            users = [x for x in db.find(AdminDBColls.USERS, filter_query, data_filter, sort=sort_list).limit(limit).skip(offset)]

        response_data = {
            'users': users
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ResendInviteAPI(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'resend_user_invite_email'
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id

        db = MongoUtility()

        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        can_resend_invite = False
        if user['company_type'] == CompanyType.PROVIDER.value:
            mapped_doc = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id}, find_one=True)
            can_resend_invite = bool(mapped_doc)
        elif user['company_id'] == admin_company_id:
            can_resend_invite = True

        if not can_resend_invite:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'User not mapped.')

        if user['is_active']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'User account is already active.')

        admin_user_data = {
            'user_name': request.user_name,
            'company_name': request.company_name,
            'company_id': request.company_id
        }

        send_new_user_invite(user, admin_user_data)

        return format_response(status.HTTP_200_OK, {}, 'Success')
