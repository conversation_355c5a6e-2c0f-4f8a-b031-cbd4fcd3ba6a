import logging
from pydantic import ValidationError
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from throttle import CustomRateThrottle
from utils import (
    format_error_response,
    format_response,
    DateUtil,
)
from utils.constants import (
    CompanyType,
    UserType,
    UserRole,
    AdminDBColls,
)
from authn.decorators import validate_query_params, validate_request_payload
from utils.mongo import MongoUtility
from schema import UserSchema, ProviderCompanySchema, CompanyMappingSchema, ProviderGroupSchema
from authn import AuthenticateSeekerAdmin
from .request_validators import (
    NewUserPayloadValidator,
    UpdateUserPayloadValidator,
    ListUsersParamsValidator,
    CreateProviderGroupValidator,
    UpdateProviderGroupValidator
)
from .utils import send_new_user_invite, send_old_user_invite

logger = logging.getLogger('application')


@method_decorator(validate_request_payload(NewUserPayloadValidator), name='post')
@method_decorator(validate_request_payload(UpdateUserPayloadValidator), name='put')
class UserCRUDAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request):
        user_data = request.validated_payload.model_dump()

        self.db = MongoUtility()

        admin_user_data = {
            'user_name': request.user_name,
            'company_name': request.company_name,
            'company_id': request.company_id
        }

        if user_data['company_type'] == CompanyType.SEEKER.value:
            # process seeker user data
            try:
                user_doc = self.create_seeker_user(user_data, admin_user_data)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            send_new_user_invite(user_doc, admin_user_data)
        else:
            # process provider user data
            try:
                is_created, user_doc = self.create_or_map_provider_user(user_data, admin_user_data)
            except ValueError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, str(e))
            except ValidationError as e:
                return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

            if is_created:
                send_new_user_invite(user_doc, admin_user_data)
            else:
                send_old_user_invite(user_doc, admin_user_data)

        return format_response(status.HTTP_200_OK, {}, 'Success')

    def create_seeker_user(self, user_data, admin_user_data):
        self.check_if_user_exists(user_data)

        user_data.update({
            'company_id': admin_user_data['company_id'],
            'company_name': admin_user_data['company_name'],
            'user_type': UserType.SEEKER.value,
            'user_role': UserRole.SEEKER.value,
        })

        user_doc = UserSchema(**user_data).model_dump()
        self.db.insert(AdminDBColls.USERS, [user_doc])
        return user_doc

    def create_or_map_provider_user(self, user_data, admin_user_data):
        existing_provider = self.check_if_user_exists(user_data)
        is_created = False

        if not existing_provider:
            company_doc = ProviderCompanySchema(name=user_data['company_name'], email=user_data['email'], phone=user_data['phone']).model_dump()

            user_data.update({
                'company_id': company_doc['id'],
                'user_type': UserType.PROVIDER.value,
                'user_role': UserRole.PROVIDER.value,
            })
            user_doc = UserSchema(**user_data).model_dump()

            self.db.insert(AdminDBColls.COMPANIES, [company_doc])
            self.db.insert(AdminDBColls.USERS, [user_doc])
            is_created = True
        else:
            self.check_if_provider_mapped(existing_provider['company_id'], admin_user_data['company_id'])
            user_doc = existing_provider

        mapping_doc = {
            'seeker_id': admin_user_data['company_id'],
            'seeker_name': admin_user_data['company_name'],
            'provider_id': user_doc['company_id'],
            'provider_name': user_doc['company_name']
        }

        mapping_doc = CompanyMappingSchema(**mapping_doc).model_dump()
        self.db.insert(AdminDBColls.COMPANY_MAPPING, [mapping_doc])
        return is_created, user_doc

    def check_if_user_exists(self, user_data):
        # check is user with same phone number exists
        user = self.db.find(AdminDBColls.USERS, {'phone': user_data['phone']}, find_one=True)
        if user:
            raise ValueError(f'User with phone: `{user["phone"]}` already exists.')

        # check is user with same email exists
        user = self.db.find(AdminDBColls.USERS, {'email': user_data['email']}, find_one=True)
        if user:
            if (user['company_type'] == CompanyType.PROVIDER.value) and (user_data['company_type'] == CompanyType.PROVIDER.value):
                return user
            else:
                # if user already exists as a seeker or
                # if user already exists as a provider but admin is trying to add the same user as seeker
                raise ValueError(f'User with email: `{user["email"]}` already exists.')
        return False

    def check_if_provider_mapped(self, provider_id, seeker_id):
        mapped_doc = self.db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': provider_id, 'seeker_id': seeker_id}, find_one=True)
        if mapped_doc:
            raise ValueError(f'Provider already mapped.')
        return False

    def put(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id
        user_data = request.validated_payload.model_dump()

        db = MongoUtility()
        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        can_update_user = True
        if user['company_type'] == CompanyType.PROVIDER.value:
            company_mapping = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id}, find_one=True)
            can_update_user = bool(company_mapping)
        elif user['company_id'] != admin_company_id:
            can_update_user = False

        if can_update_user:
            db.update(AdminDBColls.USERS, {'id': user_id}, user_data)
            return format_response(status.HTTP_200_OK, {}, 'Success')

        return format_response(status.HTTP_400_BAD_REQUEST, 'User not mapped.')

    def delete(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id

        db = MongoUtility()
        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        if user['company_type'] == CompanyType.PROVIDER.value:
            db.delete(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id})
        elif user['company_id'] == admin_company_id:
            db.delete(AdminDBColls.USERS, {'id': user_id})

        return format_response(status.HTTP_200_OK, {}, 'Success')


@method_decorator(validate_query_params(ListUsersParamsValidator), name='get')
class ListUsersApi(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request):
        validated_params = request.validated_params

        company_type = validated_params.company_type
        limit = validated_params.limit
        offset = validated_params.offset
        sort_by = validated_params.sort_by
        sort_order = validated_params.sort_order

        seeker_id = request.company_id
        sort_list = [(sort_by, sort_order)]

        db = MongoUtility()

        data_filter = {
            '_id': 0,
            'pwd_hash': 0,
            'pwd_changed_on': 0,
            'pwd_history': 0,
        }

        if company_type == CompanyType.SEEKER.value:
            filter_query = {'company_id': seeker_id, 'company_type': CompanyType.SEEKER.value}
            users = [x for x in db.find(AdminDBColls.USERS, filter_query, data_filter, sort=sort_list).limit(limit).skip(offset)]
        else:
            query = {'seeker_id': seeker_id}
            df = {'_id': 0, 'provider_id': 1}
            mapped_providers = [x['provider_id'] for x in db.find(AdminDBColls.COMPANY_MAPPING, query, df, sort=sort_list).limit(limit).skip(offset)]

            filter_query = {'company_id': {'$in': mapped_providers}, 'company_type': CompanyType.PROVIDER.value}
            users = [x for x in db.find(AdminDBColls.USERS, filter_query, data_filter, sort=sort_list).limit(limit).skip(offset)]

        response_data = {
            'users': users
        }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class ResendInviteAPI(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'resend_user_invite_email'
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        user_id = kwargs['user_id']
        admin_company_id = request.company_id

        db = MongoUtility()

        user = db.find(AdminDBColls.USERS, {'id': user_id}, find_one=True)

        if not user:
            return format_response(status.HTTP_404_NOT_FOUND, 'User not found.')

        can_resend_invite = False
        if user['company_type'] == CompanyType.PROVIDER.value:
            mapped_doc = db.find(AdminDBColls.COMPANY_MAPPING, {'provider_id': user['company_id'], 'seeker_id': admin_company_id}, find_one=True)
            can_resend_invite = bool(mapped_doc)
        elif user['company_id'] == admin_company_id:
            can_resend_invite = True

        if not can_resend_invite:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'User not mapped.')

        if user['is_active']:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'User account is already active.')

        admin_user_data = {
            'user_name': request.user_name,
            'company_name': request.company_name,
            'company_id': request.company_id
        }

        send_new_user_invite(user, admin_user_data)

        return format_response(status.HTTP_200_OK, {}, 'Success')


@method_decorator(validate_request_payload(CreateProviderGroupValidator), name='post')
@method_decorator(validate_request_payload(UpdateProviderGroupValidator), name='put')
class ProviderGroupCRUDAPI(APIView):
    """API for creating, updating and deleting provider groups"""
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request):
        """
        Create a new provider group.

        Request Body:
        {
            "name": "Group Name",
            "provider_ids": ["provider_id_1", "provider_id_2"]
        }
        """
        payload = request.validated_payload
        seeker_id = request.company_id

        db = MongoUtility()

        # Check if group with same name already exists for this seeker
        existing_group = db.find(
            AdminDBColls.PROVIDER_GROUPS,
            {'name': payload.name, 'seeker_id': seeker_id},
            find_one=True
        )

        if existing_group:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                f"Group with name '{payload.name}' already exists."
            )

        # Validate that all provider_ids are valid and mapped to this seeker
        if payload.provider_ids:
            valid_provider_ids = self._validate_provider_ids(db, payload.provider_ids, seeker_id)
            if not valid_provider_ids['is_valid']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, valid_provider_ids['error'])

        # Create group data
        group_data = {
            'name': payload.name,
            'seeker_id': seeker_id,
            'provider_ids': payload.provider_ids
        }

        try:
            group_doc = ProviderGroupSchema(**group_data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        db.insert(AdminDBColls.PROVIDER_GROUPS, [group_doc])

        return format_response(
            status.HTTP_200_OK,
            {'group_id': group_doc['id']},
            'Provider group created successfully'
        )

    def put(self, request, *args, **kwargs):
        """
        Update a provider group by adding/removing providers or updating details.

        URL Parameters:
        - group_id: Group ID from URL path

        Request Body:
        {
            "name": "Updated Group Name",
            "add_provider_ids": ["provider_id_3"],
            "remove_provider_ids": ["provider_id_1"]
        }
        """
        group_id = kwargs['group_id']
        payload = request.validated_payload
        seeker_id = request.company_id

        db = MongoUtility()

        # Check if group exists and belongs to this seeker
        group = db.find(
            AdminDBColls.PROVIDER_GROUPS,
            {'id': group_id, 'seeker_id': seeker_id},
            find_one=True
        )

        if not group:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Group not found.')

        # Prepare update data
        update_data = {}

        # Update name if provided
        if payload.name and payload.name != group['name']:
            # Check if new name conflicts with existing groups
            existing_group = db.find(
                AdminDBColls.PROVIDER_GROUPS,
                {'name': payload.name, 'seeker_id': seeker_id, 'id': {'$ne': group_id}},
                find_one=True
            )
            if existing_group:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    f"Group with name '{payload.name}' already exists."
                )
            update_data['name'] = payload.name

        # Handle provider additions and removals
        current_provider_ids = set(group.get('provider_ids', []))
        add_ids = set(payload.add_provider_ids or [])
        remove_ids = set(payload.remove_provider_ids or [])

        # Validate provider IDs to add
        if add_ids:
            valid_add_ids = self._validate_provider_ids(db, list(add_ids), seeker_id)
            if not valid_add_ids['is_valid']:
                return format_error_response(status.HTTP_400_BAD_REQUEST, valid_add_ids['error'])

        # Calculate new provider list
        new_provider_ids = (current_provider_ids | add_ids) - remove_ids
        update_data['provider_ids'] = list(new_provider_ids)
        update_data['updated_on'] = payload.updated_on

        db.update(AdminDBColls.PROVIDER_GROUPS, {'id': group_id}, update_data)

        return format_response(status.HTTP_200_OK, {}, 'Group updated successfully')

    def delete(self, request, *args, **kwargs):
        """
        Delete a provider group.

        URL Parameters:
        - group_id: Group ID from URL path
        """
        group_id = kwargs['group_id']
        seeker_id = request.company_id

        db = MongoUtility()

        # Check if group exists and belongs to this seeker
        group = db.find(
            AdminDBColls.PROVIDER_GROUPS,
            {'id': group_id, 'seeker_id': seeker_id},
            find_one=True
        )

        if not group:
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Group not found.')

        db.delete(AdminDBColls.PROVIDER_GROUPS, {'id': group_id})

        return format_response(status.HTTP_200_OK, {}, 'Group deleted successfully')

    def _validate_provider_ids(self, db, provider_ids, seeker_id):
        """
        Validate that all provider IDs are valid provider companies mapped to this seeker.

        Args:
            db: MongoUtility instance
            provider_ids: List of provider company IDs
            seeker_id: Seeker company ID

        Returns:
            dict: {'is_valid': bool, 'error': str}
        """
        if not provider_ids:
            return {'is_valid': True, 'error': None}

        # Get all mapped provider IDs for this seeker
        mapped_providers = db.find(
            AdminDBColls.COMPANY_MAPPING,
            {'seeker_id': seeker_id},
            {'provider_id': 1, '_id': 0}
        )
        valid_provider_ids = {doc['provider_id'] for doc in mapped_providers}

        # Check if all provided provider IDs are valid
        invalid_ids = [tid for tid in provider_ids if tid not in valid_provider_ids]

        if invalid_ids:
            return {
                'is_valid': False,
                'error': f"Invalid or unmapped provider IDs: {', '.join(invalid_ids)}"
            }

        return {'is_valid': True, 'error': None}
