import logging
from pydantic import ValidationError
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from authn.decorators import validate_query_params, validate_request_payload
from utils import (
    format_error_response,
    format_response,
    Memoization,
    get_next_reset_date
)
from utils.constants import (
    AdminDBColls,
    SubscriptionStatus,
    AppDomainID,
    AppPathID,
    UsageKeys
)
from utils.mongo import MongoUtility
from schema import SubscriptionSchema
from authn import AuthenticateSeekerAdmin
from .request_validators import CreateSubscriptionPayloadValidator, ListSubscriptionsParamsValidator

logger = logging.getLogger('application')


@method_decorator(validate_query_params(ListSubscriptionsParamsValidator), name='get')
class ListSubscriptionsAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request):
        params = request.validated_params
        limit = 10 if params.include_history else 1

        db = MongoUtility()

        data_filter = {
            '_id': 0,
        }
        if not params.include_features:
            data_filter['features'] = 0

        sub_docs = db.find(AdminDBColls.SUBSCRIPTIONS, {'company_id': request.company_id}, data_filter, sort=[('created_on', -1)]).limit(limit)

        subs = [x for x in sub_docs]
        if not subs:
            return format_response(status.HTTP_200_OK, subs, 'You have not Subscribed to any plans yet.')

        latest_sub = subs[0]
        if (not params.include_history) and latest_sub['status_id'] in [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]:
            usage = latest_sub.get('usage') or {}
            usage['plan_lanes_reset_on'] = get_next_reset_date(latest_sub['start_date'])
            latest_sub['usage'] = usage

        return format_response(status.HTTP_200_OK, subs, 'Success')


@method_decorator(validate_request_payload(CreateSubscriptionPayloadValidator), name='post')
class SubscriptionCRUDAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request):
        company_id = request.company_id
        payload = request.validated_payload.model_dump()

        if not request.is_email_verified:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Please verify both company and admin emails under profile management.')

        if not request.is_acc_active:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Account is still under review.')

        db = MongoUtility()

        plan_id = payload['plan_id']

        plan = db.find(AdminDBColls.PLANS, {'id': plan_id, 'is_active': True}, find_one=True)
        if not plan:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Subscription Plan not found.')

        # this filter_query shouldn't have plan_id as a user shouldn't have multiple subscriptions to the same product
        query = {
            'company_id': company_id,
            'product_id': plan['product_id'],
            'status_id': {'$in': [
                SubscriptionStatus.TRIAL.value,
                SubscriptionStatus.PENDING.value,
                SubscriptionStatus.ACTIVE.value,
                SubscriptionStatus.PAUSED.value,
                SubscriptionStatus.HALTED.value,
            ]}
        }
        data_filter = {
            '_id': 0,
            'features': 0,
        }
        existing_sub = db.find(AdminDBColls.SUBSCRIPTIONS, query, data_filter, find_one=True)

        # if status is PENDING, it means payment isn't completed or verified
        if existing_sub and (existing_sub['status_id'] != SubscriptionStatus.PENDING.value):
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Subscription already exists.')

        usage = {
            'plan_lanes': 0,
            'remaining_plan_lanes': 0,
            'purchased_lanes': 0,
            'remaining_purchased_lanes': 0
        }
        addons = []
        for feature in plan['features']:
            if feature['id'] == UsageKeys.NUMBER_OF_LANES.value:
                usage['plan_lanes'] = feature['ival']
                usage['remaining_plan_lanes'] = feature['ival']

            if feature['is_addon']:
                addons.append(feature)

        subscription_data = {**plan}
        subscription_data.update({
            'plan_name': plan['plan_name'],
            'company_id': company_id,
            'status_id': SubscriptionStatus.PENDING.value,
            'status': SubscriptionStatus.PENDING.name,
            'plan_id': plan_id,
            'auto_renew': payload['auto_renew'],
            'is_annual_sub': payload['is_annual_sub'],
            'addons': addons,
            'usage': usage,
            'created_on': request.now,
            'updated_on': request.now,
        })
        subscription_data.pop('id', None)

        try:
            subscription_doc = SubscriptionSchema(**subscription_data).model_dump()
        except ValidationError as e:
            return format_error_response(status.HTTP_400_BAD_REQUEST, pydantic_error=e)

        query['status_id'] = SubscriptionStatus.PENDING.value
        db.update(AdminDBColls.SUBSCRIPTIONS, query, subscription_doc, upsert=True)
        db.update(AdminDBColls.COMPANIES, {'id': company_id}, {'has_subscribed': True})

        subscription_doc.pop('_id', None)
        subscription_doc.pop('features', None)
        redirect_to = Memoization.get_app_url_data(AppDomainID.ADMIN, AppPathID.ADMIN_PROCEED_TO_PAY)
        return format_response(status.HTTP_200_OK, subscription_doc, 'Success', redirect_to)

    def get(self, request, *args, **kwargs):
        subscription_id = kwargs['subscription_id']

        db = MongoUtility()

        query = {
            'id': subscription_id,
            'company_id': request.company_id
        }
        sub_plan = db.find(AdminDBColls.SUBSCRIPTIONS, query, {'_id': 0, 'features': 0}, find_one=True)
        if not sub_plan:
            return format_error_response(status.HTTP_400_BAD_REQUEST, 'Subscription not found.')

        return format_response(status.HTTP_200_OK, sub_plan, 'Success')
