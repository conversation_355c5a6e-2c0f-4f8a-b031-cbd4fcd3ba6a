import logging
from rest_framework.views import APIView
from rest_framework import status
from django.utils.decorators import method_decorator
from throttle import CustomRateThrottle
from utils import (
    format_error_response,
    format_response,
    Memoization,
)
from utils.constants import (
    AdminDBColls, ValueType,
    BillingCycle,
)
from utils.mongo import MongoUtility
from authn.decorators import validate_query_params, cache_api_response
from authn import AuthenticateSeekerAdmin
from subscription import PlanService
from .request_validators import ListPlansParamsValidator

logger = logging.getLogger('application')


def process_plan_details(plan: dict, is_annual: bool = False) -> dict:
    if not is_annual:
        return plan

    plan_price = plan['price']
    annual_discount = plan['annual_discount']
    discount_factor = (1 - round(annual_discount / 100, 2))

    billing_cycles = Memoization.get_billing_cycles()
    plan['billing_cycle'] = BillingCycle.ANNUALLY.value
    plan['billing_cycle_label'] = billing_cycles.get(plan['billing_cycle'])

    if plan_price:
        discounted_price = int(round(plan_price * discount_factor))
        plan['price'] = discounted_price
        plan['pricing_label'] = f"{plan['currency'].upper()} {discounted_price}/{plan['pricing_period']}"

    for feature in plan['features']:
        if feature['apply_disc'] and (feature['ival_type'] == ValueType.PRICE.value):
            addon_price = feature['ival']
            discounted_price = int(round(addon_price * discount_factor))
            feature['ival'] = discounted_price
            feature['ilabel'] = feature['ilabel'].replace(str(addon_price), str(discounted_price))
    return plan


def restruture_plans(product_id: int, plans: list) -> tuple[list, list]:
    plan_cards, core_features = [], []

    plan_id_index_map = {}
    group_id_index_map = {}
    sub_group_id_index_map = {}
    feature_id_index_map = {}
    group_feature_id_index_map = {}  # For features directly under groups (no subgroups)
    tooltips = Memoization.get_tooltips(product_id)

    for plan_index, plan in enumerate(plans):
        group_index = 0
        plan_id = plan['id']
        plan_id_index_map[plan_id] = plan_index
        features = plan.pop('features', [])
        plan['key_features'] = []

        plan_cards.append(plan)

        for feature in features:
            if feature['is_kf']:
                fitem = {
                    'id': feature['id'],
                    'name': feature['name'],
                    'value': feature['ilabel'],
                }
                plan_index = plan_id_index_map[plan_id]
                plan_cards[plan_index]['key_features'].append(fitem)
                continue

            feature_id = feature['id']
            group_id = feature['gid']
            sub_group_id = feature.get('sgid')
            sub_group_name = feature.get('sgname')
            fvalue = feature['ilabel']

            # Create group if it doesn't exist
            if group_id not in group_id_index_map:
                gitem = {
                    'group_id': feature['gid'],
                    'group_name': feature['gname'],
                }
                core_features.append(gitem)
                group_id_index_map[group_id] = group_index
                group_index += 1

            _group_index = group_id_index_map[group_id]

            # Handle sub groups
            if sub_group_id:
                # Feature belongs to a sub group - use sub_groups structure
                # Initialize sub_groups array if not already present
                if 'sub_groups' not in core_features[_group_index]:
                    core_features[_group_index]['sub_groups'] = []

                sub_group_key = f"{group_id}_{sub_group_id}"

                # Create sub group if it doesn't exist
                if sub_group_key not in sub_group_id_index_map:
                    sub_group_item = {
                        'sub_group_id': sub_group_id,
                        'sub_group_name': sub_group_name,
                        'features': []
                    }
                    core_features[_group_index]['sub_groups'].append(sub_group_item)
                    sub_group_id_index_map[sub_group_key] = len(core_features[_group_index]['sub_groups']) - 1

                _sub_group_index = sub_group_id_index_map[sub_group_key]

                # Add feature to sub group
                if feature_id not in feature_id_index_map:
                    fitem = {
                        'name': feature['name'],
                        'tooltip': tooltips.get(feature_id),
                        'values': [],
                    }
                    core_features[_group_index]['sub_groups'][_sub_group_index]['features'].append(fitem)
                    feature_id_index_map[feature_id] = len(core_features[_group_index]['sub_groups'][_sub_group_index]['features']) - 1

                _feature_index = feature_id_index_map[feature_id]
                core_features[_group_index]['sub_groups'][_sub_group_index]['features'][_feature_index]['values'].append(fvalue)
            else:
                # Feature doesn't belong to a sub group - use original structure (group_features)
                # Initialize group_features array if not already present
                if 'group_features' not in core_features[_group_index]:
                    core_features[_group_index]['group_features'] = []

                # Add feature directly to group
                if feature_id not in group_feature_id_index_map:
                    fitem = {
                        'name': feature['name'],
                        'tooltip': tooltips.get(feature_id),
                        'values': [],
                    }
                    core_features[_group_index]['group_features'].append(fitem)
                    group_feature_id_index_map[feature_id] = len(core_features[_group_index]['group_features']) - 1

                _feature_index = group_feature_id_index_map[feature_id]
                core_features[_group_index]['group_features'][_feature_index]['values'].append(fvalue)

    return plan_cards, core_features


@method_decorator(cache_api_response(timeout=None, is_generic=True), name='get')
@method_decorator(validate_query_params(ListPlansParamsValidator), name='get')
class ListPlansAPI(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'list_plans'

    def get(self, request, *args, **kwargs):
        validated_params = request.validated_params
        product_id = validated_params.product_id
        is_annual = validated_params.is_annual

        # Get all plans for the product from cache or DB
        all_plans = PlanService.get_plans_by_product(product_id, use_cache=False)

        # Filter plans by billing cycle
        billing_cycle = BillingCycle.ANNUALLY.value if is_annual else BillingCycle.MONTHLY.value
        plans = [plan for plan in all_plans if plan.get('billing_cycle') == billing_cycle]

        # Sort plans by index
        plans = sorted(plans, key=lambda x: x.get('index', 0))

        css_config = {}
        if validated_params.restructure:
            db = MongoUtility()
            css_config = db.find(AdminDBColls.CSS_CONFIG, {'product_id': product_id, 'is_active': True}, find_one=True)
            plan_cards, core_features = restruture_plans(product_id, plans)

            response_data = {
                'plan_cards': plan_cards,
                'core_features': core_features,
                'css_config': css_config
            }
        else:
            response_data = {
                'plans': plans,
            }
        return format_response(status.HTTP_200_OK, response_data, 'Success')


class GetPlanDetailsAPI(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        plan_id = kwargs['plan_id']

        # Get plan from cache or DB using PlanService
        plan = PlanService.get_plan(plan_id)

        if not plan or not plan.get('is_active', False):
            return format_error_response(status.HTTP_404_NOT_FOUND, 'Subscription Plan not found.')

        addons = []
        for feature in plan['features']:
            if feature['is_addon']:
                addons.append(feature)

        plan['addons'] = addons
        return format_response(status.HTTP_200_OK, plan, 'Success')
