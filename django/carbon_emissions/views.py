import logging
from rest_framework import status
from pydantic import ValidationError
from rest_framework.views import APIView
from throttle import CustomRateThrottle
from utils import (
    format_error_response,
    get_google_distance,
    format_response,
    Memoization
)
from utils.constants import (
    CarbonEmissionColls,
    SAASModules
)
from utils.mongo import MongoUtility
from authn import AuthenticatePostFirstAnonymousAccess
from .request_validators import EmissionComputePayloadValidator

logger = logging.getLogger('application')


class EmissionSettingsApi(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        data = Memoization.get_emission_settings()
        return format_response(
            status.HTTP_200_OK,
            data,
            'Emission settings retrieved successfully'
        )


class EmissionVehicleTypesApi(APIView):
    throttle_classes = [CustomRateThrottle]
    throttle_scope = 'misc_open_apis'

    def get(self, request, *args, **kwargs):
        params = request.GET
        mode_type = int(params.get('mode_type') or 0)
        if not mode_type:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'missing mode_type query param'
            )

        vehicle_types = Memoization.get_emission_vehicle_types(mode_type)
        data = {'vehicle_types': vehicle_types}
        return format_response(
            status.HTTP_200_OK,
            data,
            'Vehicle types retrieved successfully'
        )


class EmissionComputeApi(APIView):
    authentication_classes = (AuthenticatePostFirstAnonymousAccess, )

    def post(self, request, *args, **kwargs):
        raw_payload = request.data
        try:
            payload = EmissionComputePayloadValidator(**raw_payload).model_dump()
        except ValidationError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                pydantic_error=e
            )

        src_place_id = payload['src_place_id']
        dst_place_id = payload['dst_place_id']
        cargo_weight = payload['cargo_weight']
        vehicle_id = payload['vehicle_id']
        mode_type = payload['mode_type']
        load_type = payload['load_type']

        query = {'vehicle_id': vehicle_id}
        db = MongoUtility(module_id=SAASModules.FREIGHT_INDEX.value)
        emission_obj = db.find(CarbonEmissionColls.EMISSION_FACTORS, query, find_one=True)
        vehicle = db.find(CarbonEmissionColls.VEHICLE_TYPES, {'id': vehicle_id}, find_one=True)
        emission_factor = emission_obj.get('factor') or 0
        if not emission_factor:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Invalid/Unknown vehicle type"
            )

        source = f'place_id:{src_place_id}'
        destination = f'place_id:{dst_place_id}'
        distance = get_google_distance(SAASModules.CARBON_EMISSIONS.value, source, destination)
        if distance is None:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Could not get the distance between source & destination"
            )

        co2e_kg = distance * cargo_weight * emission_factor
        co2e_intensity = (co2e_kg * 1000) / (cargo_weight * distance)

        data = {
            "co2e_kg": round(co2e_kg, 2),
            "co2e_intensity_g_per_ton_km": round(co2e_intensity, 2),
            "distance": distance,
            "cargo_weight_tons": cargo_weight,
            "mode_type": mode_type,
            "load_type": load_type,
            "vehicle_id": vehicle_id,
            "vehicle_type": vehicle.get('text') or vehicle.get('name'),
            "emission_factor": emission_factor
        }

        return format_response(
            status.HTTP_200_OK,
            data,
            'Emission computed successfully'
        )
