from django.urls import path, include

urlpatterns = [
    path('superadmin/', include(('superadmin.urls', 'superadmin'), namespace='superadmin')),
    path('ui-config/', include(('superadmin.ui_config.urls', 'ui_config'), namespace='ui_config')),
    path('companies/', include(('superadmin.companies.urls', 'companies'), namespace='companies')),
    path('subscriptions/', include(('superadmin.subscriptions.urls', 'subscriptions'), namespace='subscriptions')),
    path('demo-bookings/', include(('superadmin.demo_bookings.urls', 'demo_bookings'), namespace='demo_bookings')),
    path('plans/', include(('superadmin.plans.urls', 'plans'), namespace='plans')),
    path('contact-requests/', include(('superadmin.contact_requests.urls', 'contact_requests'), namespace='contact_requests')),
    path('blogs/', include(('superadmin.blogs.urls', 'blogs'), namespace='blogs')),
]
