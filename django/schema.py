from pydantic import BaseModel, EmailStr, Field, field_validator, model_validator
from typing import Literal
from typing_extensions import Self
from utils import get_uuid, DateUtil, generate_hash_id, Memoization
from utils.constants import (
    CompanyType,
    UserType,
    UserRole,
    SAASProduct,
    BillingCycle,
    DataType,
    ValueType,
    SubscriptionStatus,
    CallToAction,
    Currency,
    PricingPeriod,
    ContactRequestStatus
)


class SubGroupSchema(BaseModel):
    name: str = Field(..., description="sub group name")
    id: str = ''  # Initialize as empty string, will be set dynamically

    @model_validator(mode="after")
    def generate_id(self) -> Self:
        self.id = generate_hash_id(self.name)
        return self


class FeatureGroupSchema(BaseModel):
    name: str = Field(..., description="group name")
    product_id: SAASProduct = Field(..., description="product id")
    id: str = ''  # Initialize as empty string, will be set dynamically
    sub_groups: list[SubGroupSchema] | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    @model_validator(mode="after")
    def generate_id(self) -> Self:
        self.id = generate_hash_id(f'{self.product_id}{self.name}')
        return self

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class FeatureOptionSchema(BaseModel):
    ilabel: str = Field(..., description="item label")
    idata_type: DataType = Field(..., description="item data type")
    ival_type: ValueType = Field(..., description="item value type")
    ival: str | int | bool = Field(..., description="item value")

    @field_validator("ival", mode="after")
    def validate_ival(cls, value: str | int | bool, values) -> str | int | bool:
        # Access other field values from `values`
        try:
            data_type_class = __builtins__[values.data["idata_type"]]
        except KeyError:
            raise ValueError(f"Invalid data type")

        if not isinstance(value, data_type_class):
            raise ValueError(f"Item value must contain data corresponding to the selected data type")
        return value

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class BaseForSubPlanFeatureSchema(BaseModel):
    id: str
    is_cfg: bool = Field(default=False, description="is_configurable - signifies whether the end user is allowed to enable/disable the feature post subscription")
    has_bl: bool = Field(default=False, description="has_business_logic - signifies whether the feature has a business logic to be implemented in the backend or not")
    track_usage: bool = Field(default=False, description="prerequisite - is_bl_coded=True | signifies whether the feature usage and limits need to be tracked")
    apply_disc: bool = Field(default=False, description="signifies whether annual discount is applicable on the feature if user takes an annual subscription")
    is_addon: bool = Field(default=False, description="signifies whether the feature is an addon for which user can choose to pay additional amount")
    addon_toggle: bool = Field(default=False, description="prerequisite - is_addon=True | signifies whether to render the toggle option in the UI for new subscriptio")


class BaseForFeatureSchema(BaseForSubPlanFeatureSchema):
    name: str = Field(..., description="feature name")
    gid: str = Field(..., description="group id")
    gname: str = Field(..., description="group name")
    sgid: str | None = Field(default=None, description="sub group id")
    sgname: str | None = Field(default=None, description="sub group name")
    is_kf: bool = Field(default=False, description="is_key_feature - signifies whether its a key_feature=True or a core_feature=False")


class FeatureSchema(BaseForFeatureSchema):
    id: str = ''  # Initialize as empty string, will be set dynamically
    product_id: SAASProduct = Field(..., description="product id")
    options: list[FeatureOptionSchema]
    is_active: bool = Field(default=True, description="signifies whether the feature is activated and can be added to the plan")
    is_bl_coded: bool = Field(default=False, description="prerequisite - has_bl=True | signifies whether the business logic of the feature has been implemented or not")
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @model_validator(mode="after")
    def generate_id(self) -> Self:
        self.id = generate_hash_id(f"{self.product_id}{self.gname}{self.sgname or ''}{self.name}")
        return self

    @field_validator("is_bl_coded", mode="before")
    def set_is_bl_coded(cls, value: bool) -> bool:
        # Always set the value to False, regardless of input during initial document creation in DB
        # needs to be enabled manually by developer in DB post implementation of the feature specific business logic
        value = False
        return value


class PlanFeatureSchema(BaseForFeatureSchema):
    ilabel: str = Field(..., description="item label")
    ival_type: ValueType = Field(..., description="item value type")
    ival: str | int | bool = Field(..., description="item value")

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class AddonSchema(BaseModel):
    id: str = Field(..., description="feature id")
    name: str = Field(..., description="feature name")
    is_paid: bool = Field(default=False, description="signifies whether the user has paid for the selected addon or not")
    is_enabled: bool = Field(default=False, description="signifies whether the user has enabled/disabled the addon")
    is_mandatory: bool = Field(default=False, description="signifies whether the feature is a mandatory addon for which user has to pay additional amount")
    is_one_time: bool = Field(default=False, description="signifies whether its an addon feature for which user has to pay additional amount only once")
    apply_disc: bool = Field(default=False, description="signifies whether annual discount is applicable on the feature if user takes an annual subscription")
    is_qty_cfg: bool = Field(default=False, description="is_quantity_configurable - signifies whether user can increase the quantity of the addon")
    qty: int = Field(default=1, gt=0, description="signifies the quantity of the addon, must be a positive integer greater than 0")
    price: int = Field(..., description="price of the addon")


class CTAButtonSchema(BaseModel):
    label: str
    action: CallToAction


class PlanSchema(BaseModel):
    plan_name: str
    plan_text_id: str
    product_name: str = Field(..., description="product name")
    product_id: SAASProduct = Field(..., description="product id")
    id: str = ''  # Initialize as empty string, will be set dynamically
    index: int = Field(default=0, description="signifies the order in which the plans need to be displayed")
    is_active: bool = False
    annual_discount: int = Field(default=0, description="annual discount in percentage")
    price: int | None = Field(default=None, description="actual price per month. for annual subscriptions, annual price to be computed during payment")
    pricing_period: PricingPeriod | None = Field(default=PricingPeriod.MONTH)
    currency: Currency = Field(default=Currency.INR)
    pricing_label: str = Field(..., description="custom label to display the plan pricing")
    billing_cycle: BillingCycle
    billing_cycle_label: str = ''
    addons: list[PlanFeatureSchema]
    features: list[PlanFeatureSchema]
    razorpay_plan_id: str | None = None
    cta_button: CTAButtonSchema  # call to action
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @model_validator(mode="after")
    def generate_id(self) -> Self:
        self.id = f'{self.product_id}{self.plan_name[:2]}{self.billing_cycle[0].upper()}{get_uuid()}'
        return self

    @field_validator("billing_cycle_label", mode="after")
    def set_billing_cycle_label(cls, value: str, values) -> str:
        billing_cycles = Memoization.get_billing_cycles()
        return billing_cycles.get(values.data['billing_cycle'], value)

    @field_validator("pricing_label", mode="after")
    def set_pricing_label(cls, value: str, values) -> str:
        price = values.data['price']
        if (not value) and price:
            value = f"{values.data['currency'].upper()} {price}/{values.data['pricing_period']}"
        return value


class SubPlanFeatureSchema(BaseForSubPlanFeatureSchema):
    ival_type: ValueType = Field(..., description="item value type")
    ival: str | int | bool = Field(..., description="item value")

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class UsageStats(BaseModel):
    plan_lanes: int
    remaining_plan_lanes: int
    purchased_lanes: int = 0
    remaining_purchased_lanes: int = 0


class SubscriptionSchema(BaseModel):
    plan_name: str
    plan_text_id: str
    product_name: str = Field(..., description="product name")
    product_id: SAASProduct = Field(..., description="product id")
    id: str = Field(default_factory=get_uuid)
    company_id: str
    status_id: SubscriptionStatus
    status: str
    start_date: int | None = Field(default=None, description="signifies subscription trial validity start date")
    end_date: int | None = Field(default=None, description="signifies subscription trial validity end date")
    auto_renew: bool = False
    is_annual_sub: bool = Field(default=False, description="whether the user has taken an annual or monthly subscription")
    plan_id: str
    annual_discount: int = Field(default=0, description="annual discount in percentage")
    price: float = Field(..., description="actual price per month. for annual subscriptions, annual price to be computed during payment")
    pricing_period: PricingPeriod | None = Field(default=PricingPeriod.MONTH)
    currency: Currency | None = Field(default=Currency.INR)
    billing_cycle: BillingCycle
    billing_cycle_label: str
    addons: list[SubPlanFeatureSchema]
    usage: UsageStats
    razorpay_sub_id: str | None = None
    current_start: int | None = Field(default=None, description="signifies payment validity start date i.e the time of latest payment")
    current_end: int | None = Field(default=None, description="signifies payment validity end date i.e from the time of latest payment")
    is_authenticated: bool | None = Field(default=False, description="signifies whether payment is completed or not")
    remarks: str | None = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values

    @field_validator("status", mode="after")
    def set_status(cls, value: str, values) -> str:
        value = SubscriptionStatus(values.data["status_id"]).name
        return value

    @field_validator("billing_cycle_label", mode="after")
    def set_billing_cycle_label(cls, value: str, values) -> str:
        billing_cycles = Memoization.get_billing_cycles()
        return billing_cycles.get(values.data['billing_cycle'], value)


class ProviderCompanySchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    is_active: bool = True
    company_type: Literal[CompanyType.PROVIDER.value] = CompanyType.PROVIDER.value
    name: str
    email: EmailStr
    phone: int | None = None
    email_verified_on: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class UserSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    is_active: bool = False
    company_id: str
    company_name: str
    company_type: CompanyType
    user_name: str
    email: EmailStr
    phone: int
    user_type: UserType
    user_role: UserRole
    pwd_hash: str = None
    pwd_changed_on: str = None
    pwd_history: list[str] = []
    email_verified_on: int = None
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class CompanyMappingSchema(BaseModel):
    seeker_id: str
    seeker_name: str
    provider_id: str
    provider_name: str
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class ProviderGroupSchema(BaseModel):
    id: str = Field(default_factory=get_uuid)
    name: str = Field(..., min_length=1, max_length=100, description="Group name")
    seeker_id: str = Field(..., description="ID of the seeker company that owns this group")
    provider_ids: list[str] = Field(default=[], description="List of provider company IDs in this group")
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)


class PaidAddonSchema(BaseModel):
    id: str
    is_paid: bool = Field(default=False, description="signifies whether the user has paid for the selected addon or not")
    is_one_time: bool = Field(default=False, description="signifies whether its an addon feature for which user has to pay additional amount only once")
    paid_on: int | None = None
    start_date: int | None = Field(default=None, description="signifies the addons validity start date if paid")
    end_date: int | None = Field(default=None, description="signifies the addons validity end date if paid")


class CompanySettingsSchema(BaseModel):
    company_id: str
    company_name: str
    paid_addons: list[PaidAddonSchema] | None = []


class FeatureSettingsSchema(BaseModel):
    company_id: str
    product_id: SAASProduct = Field(..., description="product id")
    id: str = Field(..., description="feature id")
    name: str = Field(..., description="feature name")
    gid: str = Field(..., description="group id")
    gname: str = Field(..., description="group name")
    value: bool | str | int | float = Field(..., description="feature value")

    class Config:
        use_enum_values = True  # Automatically resolve enums to their values


class ContactRequestSchema(BaseModel):
    name: str
    email: EmailStr
    contact: str
    company_name: str
    message: str
    status: int = ContactRequestStatus.PENDING.value
    created_on: int = Field(default_factory=DateUtil.get_current_timestamp)
    updated_on: int = Field(default_factory=DateUtil.get_current_timestamp)
