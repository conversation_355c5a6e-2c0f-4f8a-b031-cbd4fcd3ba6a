from django.conf import settings
from subscription import PlanService
from utils import RedisUtils


# python manage.py runscript refresh_cache

def run():
    # clear admin django redis cache
    admin_django_rd = RedisUtils(settings.DJANGO_CACHE_DB_URL)
    admin_django_rd.delete_data(flush_db=True)

    rd = RedisUtils(settings.REDIS_SAAS_DB_URL)

    # delete existing plan keys
    for key in rd.redis_conn.scan_iter("plan:*"):
        rd.delete_data(key)

    plans = PlanService.cache_all_plans()
    return f'Cached {len(plans)} plans'
