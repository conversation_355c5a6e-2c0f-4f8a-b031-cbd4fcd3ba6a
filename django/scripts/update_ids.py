#!/usr/bin/env python3
"""
Script to update sub group IDs, group IDs, and feature IDs of existing data in the database.

This script regenerates IDs for:
1. Feature Groups (based on product_id + name)
2. Sub Groups (based on name)
3. Features (based on product_id + gname + sgname + name)

It also updates all references to these IDs in plans.

Usage:
    python manage.py runscript update_ids

Arguments:
    --dry-run: Show what would be updated without making changes
    --product-id: Only update data for specific product ID (optional)
    --verbose: Show detailed output
"""

import sys
import logging
import traceback
from typing import Dict, List, Optional
from utils import generate_hash_id, DateUtil
from utils.constants import AdminDBColls, SAASProduct
from utils.mongo import MongoUtility

logger = logging.getLogger('application')


class IDUpdater:
    """Class to handle updating IDs in the database"""

    def __init__(self, dry_run: bool = False, product_id: Optional[int] = None, verbose: bool = False):
        self.dry_run = dry_run
        self.product_id = product_id
        self.verbose = verbose
        self.db = MongoUtility()

        # Track changes for reporting
        self.changes = {
            'feature_groups': [],
            'features': [],
            'plans': [],
        }

    def log(self, message: str, level: str = 'info'):
        """Log message with appropriate level"""
        if self.verbose or level in ['warning', 'error']:
            print(f"[{level.upper()}] {message}")

        if level == 'error':
            logger.error(message)
        elif level == 'warning':
            logger.warning(message)
        else:
            logger.info(message)

    def get_feature_groups(self) -> List[Dict]:
        """Get all feature groups, optionally filtered by product_id"""
        query = {}
        if self.product_id:
            query['product_id'] = self.product_id

        groups = list(self.db.find(AdminDBColls.FEATURE_GROUPS, query, None))
        self.log(f"Found {len(groups)} feature groups to process")
        return groups

    def get_features(self) -> List[Dict]:
        """Get all features, optionally filtered by product_id"""
        query = {}
        if self.product_id:
            query['product_id'] = self.product_id

        features = list(self.db.find(AdminDBColls.FEATURES, query, None))
        self.log(f"Found {len(features)} features to process")
        return features

    def update_feature_group_ids(self) -> Dict[str, str]:
        """
        Update feature group IDs and sub-group IDs.
        Returns mapping of old_id -> new_id for feature groups.
        """
        groups = self.get_feature_groups()
        id_mapping = {}

        for group in groups:
            old_id = group['id']
            product_id = group['product_id']
            name = group['name']

            # Generate new group ID
            new_id = generate_hash_id(f'{product_id}{name}')

            if old_id != new_id:
                id_mapping[old_id] = new_id

                # Update sub-group IDs if they exist
                updated_sub_groups = []
                if group.get('sub_groups'):
                    for sub_group in group['sub_groups']:
                        old_sg_id = sub_group['id']
                        new_sg_id = generate_hash_id(sub_group['name'])

                        if old_sg_id != new_sg_id:
                            sub_group['id'] = new_sg_id
                            self.log(f"Sub-group ID: {old_sg_id} -> {new_sg_id}")

                        updated_sub_groups.append(sub_group)

                # Prepare update data
                update_data = {
                    'id': new_id,
                    'updated_on': DateUtil.get_current_timestamp()
                }

                if updated_sub_groups:
                    update_data['sub_groups'] = updated_sub_groups

                self.changes['feature_groups'].append({
                    'old_id': old_id,
                    'new_id': new_id,
                    'name': name,
                    'product_id': product_id
                })

                self.log(f"Feature Group ID: {old_id} -> {new_id} (Product: {product_id}, Name: {name})")

                if not self.dry_run:
                    result = self.db.update(
                        AdminDBColls.FEATURE_GROUPS,
                        {'_id': group['_id']},
                        set_query=update_data
                    )
                    self.log(f"Updated feature group: {result}")

        return id_mapping

    def update_feature_ids(self) -> Dict[str, str]:
        """
        Update feature IDs.
        Returns mapping of old_id -> new_id for features.
        """
        features = self.get_features()
        id_mapping = {}

        for feature in features:
            old_id = feature['id']
            gname = feature['gname']
            product_id = feature['product_id']
            sgname = feature.get('sgname', '') or ''
            name = feature['name']

            # Generate new feature ID
            new_id = generate_hash_id(f"{product_id}{gname}{sgname}{name}")

            if old_id != new_id:
                id_mapping[old_id] = new_id

                update_data = {
                    'id': new_id,
                    'updated_on': DateUtil.get_current_timestamp()
                }

                self.changes['features'].append({
                    'old_id': old_id,
                    'new_id': new_id,
                    'name': name,
                    'gname': gname,
                    'sgname': sgname
                })

                self.log(f"Feature ID: {old_id} -> {new_id} (Group: {gname}, SubGroup: {sgname}, Name: {name})")

                if not self.dry_run:
                    result = self.db.update(
                        AdminDBColls.FEATURES,
                        {'_id': feature['_id']},
                        set_query=update_data
                    )
                    self.log(f"Updated feature: {result}")

        return id_mapping

    def update_plans_with_new_feature_ids(self, feature_id_mapping: Dict[str, str], group_id_mapping: Dict[str, str]):
        """Update plans to use new feature IDs and group IDs"""
        if not feature_id_mapping and not group_id_mapping:
            self.log("No ID mappings provided, skipping plan updates")
            return

        query = {}
        if self.product_id:
            query['product_id'] = self.product_id

        plans = list(self.db.find(AdminDBColls.PLANS, query, None))
        self.log(f"Found {len(plans)} plans to check for updates")

        for plan in plans:
            plan_updated = False
            update_data = {}

            # Update features list
            if plan.get('features'):
                updated_features = []
                for feature in plan['features']:
                    old_feature_id = feature.get('id')
                    if old_feature_id and old_feature_id in feature_id_mapping:
                        feature['id'] = feature_id_mapping[old_feature_id]
                        plan_updated = True
                        self.log(f"Plan {plan['id']}: Updated feature ID {old_feature_id} -> {feature['id']}")

                    # Update gid if group ID mapping exists
                    old_gid = feature.get('gid')
                    if old_gid and old_gid in group_id_mapping:
                        feature['gid'] = group_id_mapping[old_gid]
                        plan_updated = True
                        self.log(f"Plan {plan['id']}: Updated group ID {old_gid} -> {feature['gid']}")

                    updated_features.append(feature)

                if plan_updated:
                    update_data['features'] = updated_features

            # Update addons list
            if plan.get('addons'):
                updated_addons = []
                for addon in plan['addons']:
                    old_addon_id = addon.get('id')
                    if old_addon_id and old_addon_id in feature_id_mapping:
                        addon['id'] = feature_id_mapping[old_addon_id]
                        plan_updated = True
                        self.log(f"Plan {plan['id']}: Updated addon ID {old_addon_id} -> {addon['id']}")

                    # Update gid if group ID mapping exists
                    old_gid = addon.get('gid')
                    if old_gid and old_gid in group_id_mapping:
                        addon['gid'] = group_id_mapping[old_gid]
                        plan_updated = True
                        self.log(f"Plan {plan['id']}: Updated addon group ID {old_gid} -> {addon['gid']}")

                    updated_addons.append(addon)

                if plan_updated:
                    update_data['addons'] = updated_addons

            if plan_updated:
                update_data['updated_on'] = DateUtil.get_current_timestamp()

                self.changes['plans'].append({
                    'plan_id': plan['id'],
                    'plan_name': plan.get('plan_name', 'Unknown'),
                    'updates': len([k for k in update_data.keys() if k != 'updated_on'])
                })

                if not self.dry_run:
                    result = self.db.update(
                        AdminDBColls.PLANS,
                        {'_id': plan['_id']},
                        set_query=update_data
                    )
                    self.log(f"Updated plan {plan['id']}: {result}")

    def run_update(self):
        """Main method to run the ID update process"""
        self.log("Starting ID update process...")

        if self.product_id:
            # Validate product_id
            try:
                product_enum = SAASProduct(self.product_id)
                self.log(f"Updating IDs for product: {product_enum.name} (ID: {self.product_id})")
            except ValueError:
                self.log(f"Invalid product_id: {self.product_id}. Valid values: {[p.value for p in SAASProduct]}", 'error')
                return False
        else:
            self.log("Updating IDs for all products")

        try:
            # Step 1: Update feature group IDs (including sub-group IDs)
            self.log("\n--- Step 1: Updating Feature Group IDs ---")
            group_id_mapping = self.update_feature_group_ids()

            # Step 2: Update feature IDs
            self.log("\n--- Step 2: Updating Feature IDs ---")
            feature_id_mapping = self.update_feature_ids()

            # Step 3: Update plans with new IDs
            self.log("\n--- Step 3: Updating Plans ---")
            self.update_plans_with_new_feature_ids(feature_id_mapping, group_id_mapping)

            # Print summary
            self.print_summary()

            self.log("ID update process completed successfully!")
            return True

        except Exception as e:
            self.log(f"Error during ID update process: {str(e)}", 'error')
            self.log(f"Traceback: {traceback.format_exc()}", 'error')
            return False

    def print_summary(self):
        """Print summary of all changes"""
        print("\n" + "=" * 80)
        print("SUMMARY OF CHANGES")
        print("=" * 80)

        if self.dry_run:
            print("*** DRY RUN MODE - NO ACTUAL CHANGES MADE ***\n")

        # Feature Groups
        if self.changes['feature_groups']:
            print(f"Feature Groups Updated: {len(self.changes['feature_groups'])}")
            for change in self.changes['feature_groups']:
                print(f"  - {change['name']} (Product {change['product_id']}): {change['old_id']} -> {change['new_id']}")
        else:
            print("Feature Groups Updated: 0")

        # Features
        if self.changes['features']:
            print(f"\nFeatures Updated: {len(self.changes['features'])}")
            for change in self.changes['features']:
                print(f"  - {change['name']} ({change['gname']}/{change['sgname']}): {change['old_id']} -> {change['new_id']}")
        else:
            print("\nFeatures Updated: 0")

        # Plans
        if self.changes['plans']:
            print(f"\nPlans Updated: {len(self.changes['plans'])}")
            for change in self.changes['plans']:
                print(f"  - {change['plan_name']} ({change['plan_id']}): {change['updates']} updates")
        else:
            print("\nPlans Updated: 0")

        print("\n" + "=" * 80)


def parse_arguments():
    """Parse command line arguments"""
    import argparse

    parser = argparse.ArgumentParser(description='Update sub group IDs, group IDs, and feature IDs in the database')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')
    parser.add_argument('--product-id', type=int, help='Only update data for specific product ID')
    parser.add_argument('--verbose', action='store_true', help='Show detailed output')

    return parser.parse_args()


def run():
    """Entry point for Django's runscript command"""
    import os

    # For Django runscript, we'll use environment variables or interactive prompts
    # since runscript doesn't handle arguments well

    print("ID Update Script")
    print("================")
    print()

    # Check for environment variables first
    dry_run = os.environ.get('DRY_RUN', '').lower() in ['true', '1', 'yes']
    verbose = os.environ.get('VERBOSE', '').lower() in ['true', '1', 'yes']
    product_id = None

    product_id_env = os.environ.get('PRODUCT_ID')
    if product_id_env:
        try:
            product_id = int(product_id_env)
        except ValueError:
            print(f"Error: Invalid PRODUCT_ID environment variable: {product_id_env}")
            return

    # If no environment variables set, prompt user for options
    if not any([dry_run, verbose, product_id]):
        print("No environment variables set. You can set:")
        print("  export DRY_RUN=true")
        print("  export VERBOSE=true")
        print("  export PRODUCT_ID=1")
        print()

        # Interactive prompts
        try:
            dry_run_input = input("Run in dry-run mode? (y/N): ").strip().lower()
            dry_run = dry_run_input in ['y', 'yes', 'true', '1']

            verbose_input = input("Enable verbose output? (y/N): ").strip().lower()
            verbose = verbose_input in ['y', 'yes', 'true', '1']

            product_input = input("Enter product ID (or press Enter for all products): ").strip()
            if product_input:
                try:
                    product_id = int(product_input)
                except ValueError:
                    print(f"Error: Invalid product ID: {product_input}")
                    return
        except (KeyboardInterrupt, EOFError):
            print("\nOperation cancelled.")
            return

    print(f"Configuration:")
    print(f"  Dry Run: {dry_run}")
    print(f"  Product ID: {product_id if product_id else 'All products'}")
    print(f"  Verbose: {verbose}")
    print()

    if not dry_run:
        try:
            confirm = input("This will modify the database. Continue? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                print("Operation cancelled.")
                return
        except (KeyboardInterrupt, EOFError):
            print("\nOperation cancelled.")
            return

    # Create and run updater
    updater = IDUpdater(dry_run=dry_run, product_id=product_id, verbose=verbose)
    success = updater.run_update()

    if not success:
        sys.exit(1)


if __name__ == '__main__':
    # For direct execution
    args = parse_arguments()

    print("ID Update Script")
    print("================")
    print(f"Dry Run: {args.dry_run}")
    print(f"Product ID: {args.product_id if args.product_id else 'All products'}")
    print(f"Verbose: {args.verbose}")
    print()

    # Create and run updater
    updater = IDUpdater(dry_run=args.dry_run, product_id=args.product_id, verbose=args.verbose)
    success = updater.run_update()

    if not success:
        sys.exit(1)
