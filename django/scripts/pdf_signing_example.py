#!/usr/bin/env python
"""
Example integration of PDF signing functionality

This example shows how to integrate the PDF signing script into your Django application.
"""

import os
import logging
from django.conf import settings
from scripts.digitally_sign_pdf import sign_pdf_document

logger = logging.getLogger('application')


class DocumentProcessor:
    """Example class showing how to integrate PDF signing into your application"""
    
    def __init__(self):
        self.signed_documents = []
        self.failed_documents = []
    
    def process_document(self, pdf_filename, auto_sign=True):
        """
        Process a document and optionally sign it
        
        Args:
            pdf_filename (str): Name of the PDF file to process
            auto_sign (bool): Whether to automatically sign the document
        
        Returns:
            dict: Processing result
        """
        try:
            # Validate document exists
            input_path = os.path.join(settings.MEDIA_ROOT, pdf_filename)
            if not os.path.exists(input_path):
                return {
                    'success': False,
                    'error': f'Document not found: {pdf_filename}',
                    'signed': False
                }
            
            result = {
                'success': True,
                'document': pdf_filename,
                'signed': False,
                'original_path': input_path
            }
            
            if auto_sign:
                # Sign the document
                signing_result = sign_pdf_document(pdf_filename)
                
                if signing_result['success']:
                    result.update({
                        'signed': True,
                        'signed_path': signing_result['output_file'],
                        'signed_filename': signing_result['output_filename']
                    })
                    self.signed_documents.append(pdf_filename)
                    logger.info(f'Successfully processed and signed: {pdf_filename}')
                else:
                    result.update({
                        'signing_error': signing_result['error']
                    })
                    self.failed_documents.append(pdf_filename)
                    logger.error(f'Failed to sign document {pdf_filename}: {signing_result["error"]}')
            
            return result
            
        except Exception as e:
            logger.error(f'Error processing document {pdf_filename}: {str(e)}')
            return {
                'success': False,
                'error': str(e),
                'signed': False
            }
    
    def batch_process_documents(self, pdf_filenames, auto_sign=True):
        """
        Process multiple documents in batch
        
        Args:
            pdf_filenames (list): List of PDF filenames to process
            auto_sign (bool): Whether to automatically sign documents
        
        Returns:
            dict: Batch processing results
        """
        results = []
        successful = 0
        failed = 0
        signed = 0
        
        for filename in pdf_filenames:
            result = self.process_document(filename, auto_sign)
            results.append(result)
            
            if result['success']:
                successful += 1
                if result.get('signed'):
                    signed += 1
            else:
                failed += 1
        
        return {
            'total_processed': len(pdf_filenames),
            'successful': successful,
            'failed': failed,
            'signed': signed,
            'results': results,
            'signed_documents': self.signed_documents.copy(),
            'failed_documents': self.failed_documents.copy()
        }
    
    def get_processing_summary(self):
        """Get a summary of processed documents"""
        return {
            'total_signed': len(self.signed_documents),
            'total_failed': len(self.failed_documents),
            'signed_documents': self.signed_documents.copy(),
            'failed_documents': self.failed_documents.copy()
        }


def example_usage():
    """Example usage of the document processor"""
    processor = DocumentProcessor()
    
    # Process a single document
    result = processor.process_document('invoice_001.pdf')
    print(f"Single document result: {result}")
    
    # Process multiple documents
    documents = ['contract_001.pdf', 'agreement_002.pdf', 'invoice_003.pdf']
    batch_result = processor.batch_process_documents(documents)
    print(f"Batch processing result: {batch_result}")
    
    # Get summary
    summary = processor.get_processing_summary()
    print(f"Processing summary: {summary}")


# Example Django view integration
def example_django_view_integration():
    """
    Example showing how to integrate PDF signing into a Django view
    """
    from django.http import JsonResponse
    from django.views import View
    
    class SignDocumentView(View):
        def post(self, request):
            pdf_filename = request.POST.get('pdf_filename')
            
            if not pdf_filename:
                return JsonResponse({
                    'success': False,
                    'error': 'PDF filename is required'
                }, status=400)
            
            # Sign the document
            result = sign_pdf_document(pdf_filename)
            
            if result['success']:
                return JsonResponse({
                    'success': True,
                    'message': 'Document signed successfully',
                    'signed_filename': result['output_filename'],
                    'download_url': f'/media/{result["output_filename"]}'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': result['error']
                }, status=500)
    
    return SignDocumentView


# Example Celery task integration
def example_celery_task():
    """
    Example showing how to integrate PDF signing into a Celery task
    """
    from celery import task
    
    @task(bind=True, max_retries=3)
    def sign_document_task(self, pdf_filename):
        """
        Celery task to sign a PDF document
        """
        try:
            result = sign_pdf_document(pdf_filename)
            
            if result['success']:
                logger.info(f'Task completed: Document {pdf_filename} signed successfully')
                return {
                    'status': 'SUCCESS',
                    'result': result
                }
            else:
                logger.error(f'Task failed: {result["error"]}')
                return {
                    'status': 'FAILURE',
                    'error': result['error']
                }
                
        except Exception as e:
            logger.error(f'Task error: {str(e)}')
            # Retry the task
            raise self.retry(countdown=60, exc=e)
    
    return sign_document_task


if __name__ == '__main__':
    # Run example usage
    example_usage()
