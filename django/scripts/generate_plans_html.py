"""
Django script to generate HTML file with plans comparison table for a given product ID.

Usage:
    python manage.py runscript generate_plans_html --script-args <product_id>

Example:
    python manage.py runscript generate_plans_html --script-args 2
"""

import json
from datetime import datetime
from subscription.plan_service import PlanService


def get_product_plans_and_features(product_id):
    """
    Get all plans and features for a specific product.

    Args:
        product_id (int): The product ID

    Returns:
        list: plans
    """
    # Get all active plans for the product
    plans = PlanService.get_plans_by_product(product_id, use_cache=False)
    return plans


def organize_features_by_plans(plans, refer_plan):
    """
    Organize features by plan types and create a comprehensive feature matrix.

    Args:
        plans (list): List of plan documents

    Returns:
        dict: Organized feature data
    """
    plan_types = ['micro', 'small', 'medium', 'enterprise']
    plan_mapping = {}

    # Map actual plan names to our standard types
    for plan in plans:
        plan_mapping[plan['plan_text_id']] = plan

    # Create feature matrix
    feature_matrix = []

    for feature_data in plan_mapping[refer_plan]['features']:
        row = {
            **feature_data,
            'values': {}
        }

        # Get values for each plan type
        for plan_type in plan_types:
            plan = plan_mapping[plan_type]
            plan_feature = next(
                (f for f in plan.get('features', []) if f['id'] == feature_data['id']),
                None
            )
            if plan_feature:
                row['values'][plan_type] = plan_feature.get('ilabel', 'NO_DATA')
            else:
                row['values'][plan_type] = 'NO_DATA'

        feature_matrix.append(row)
    return feature_matrix, plan_mapping


def generate_html(product_id, feature_matrix, plan_mapping):
    """
    Generate HTML file with the plans comparison table.

    Args:
        product_id (int): The product ID
        feature_matrix (list): List of feature data
        plan_mapping (dict): Mapping of plan types to plan data
    """
    # Get product name
    product_names = {
        1: "Network",
        2: "Logistics Procurement",
        3: "Optimization",
        4: "Execution",
        5: "Visibility",
        6: "Reconciliation",
        7: "Analytics",
        8: "Orchestration",
        9: "ILMS"
    }

    product_name = product_names.get(product_id, f"Product {product_id}")

    # Get actual plan names for headers
    plan_headers = {}
    for plan_type in ['micro', 'small', 'medium', 'enterprise']:
        if plan_type in plan_mapping:
            plan_headers[plan_type] = plan_mapping[plan_type]['plan_name']
        else:
            plan_headers[plan_type] = plan_type.title()

    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{product_name} - Plans Comparison</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }}
        .container {{
            background: #2d2d2d;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.5);
            border: 1px solid #404040;
        }}
        h1 {{
            color: #ffffff;
            text-align: center;
            margin-bottom: 30px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            border: 1px solid #404040;
            padding: 12px;
            text-align: left;
            vertical-align: top;
            color: #e0e0e0;
        }}
        th {{
            background-color: #3a3a3a;
            font-weight: bold;
            color: #ffffff;
        }}
        tr:nth-child(even) {{
            background-color: #333333;
        }}
        tr:hover {{
            background-color: #404040;
        }}
        .sl-no {{
            text-align: center;
            width: 60px;
        }}
        .group-name {{
            width: 200px;
            font-weight: 500;
        }}
        .subgroup-name {{
            width: 200px;
        }}
        .feature-name {{
            width: 250px;
            font-weight: 500;
        }}
        .plan-value {{
            width: 150px;
            text-align: center;
        }}
        .na {{
            color: #ff6b6b;
            font-weight: bold;
        }}
        .info {{
            margin-bottom: 20px;
            padding: 15px;
            background-color: #3a3a3a;
            border-radius: 4px;
            border: 1px solid #404040;
            color: #e0e0e0;
        }}
        .update-feature-btn {{
            background-color: #339af0;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            margin-left: 5px;
            transition: background-color 0.2s;
        }}
        .update-feature-btn:hover {{
            background-color: #228be6;
        }}
        .add-feature-btn {{
            background-color: red;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
            margin-left: 5px;
            transition: background-color 0.2s;
        }}
        .add-feature-btn:hover {{
            background-color: green;
        }}
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }}
        .modal-content {{
            background-color: #2d2d2d;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80%;
            overflow-y: auto;
            border: 1px solid #404040;
            color: #e0e0e0;
        }}
        .close {{
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }}
        .close:hover {{
            color: #ffffff;
        }}
        .query-box {{
            background-color: #1a1a1a;
            border: 1px solid #404040;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            color: #e0e0e0;
        }}
        .copy-btn {{
            background-color: #51cf66;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            margin-left: 80%;
            transition: background-color 0.2s;
        }}
        .copy-btn:hover {{
            background-color: #40c057;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{product_id} - {product_name} - Plans Comparison</h1>

        <div class="info">
            <strong>Generated on:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
            <strong>Product ID:</strong> {product_id}<br>
            <strong>Total Features:</strong> {len(feature_matrix)}
        </div>

        <table>
            <thead>
                <tr>
                    <th class="sl-no">Sl No</th>
                    <th class="group-name">Group Name</th>
                    <th class="subgroup-name">Sub Group Name</th>
                    <th class="feature-name">Feature Name</th>
                    <th class="plan-value">{plan_headers['micro']}({len(plan_mapping['micro']['features'])})</th>
                    <th class="plan-value">{plan_headers['small']}({len(plan_mapping['small']['features'])})</th>
                    <th class="plan-value">{plan_headers['medium']}({len(plan_mapping['medium']['features'])})</th>
                    <th class="plan-value">{plan_headers['enterprise']}({len(plan_mapping['enterprise']['features'])})</th>
                </tr>
            </thead>
            <tbody>"""

    for i, feature in enumerate(feature_matrix):
        def format_value(value, plan_type, feature_data):
            fname = feature_data["name"].replace("'", "")
            gname = feature_data["gname"].replace("'", "")
            sgname = (feature_data.get("sgname") or '').replace("'", "")
            if value == 'NO_DATA':
                return f'<span class="na">{value}</span><button class="add-feature-btn" onclick="showAddFeatureModal(\'{product_id}\', \'{feature_data["id"]}\', \'{plan_type}\', \'{fname}\', \'{gname}\', \'{sgname}\')">Add</button>'
            else:
                return f'<span>{value}</span><button class="update-feature-btn" onclick="showAddFeatureModal(\'{product_id}\', \'{feature_data["id"]}\', \'{plan_type}\', \'{fname}\', \'{gname}\', \'{sgname}\')">Update</button>'

        html_content += f"""
                <tr>
                    <td class="sl-no">{i}</td>
                    <td class="group-name">{feature['gname']}</td>
                    <td class="subgroup-name">{feature.get('sgname') or '-'}</td>
                    <td class="feature-name">{feature['name']}</td>
                    <td class="plan-value">{format_value(feature['values'].get('micro', 'N/A'), 'micro', feature)}</td>
                    <td class="plan-value">{format_value(feature['values'].get('small', 'N/A'), 'small', feature)}</td>
                    <td class="plan-value">{format_value(feature['values'].get('medium', 'N/A'), 'medium', feature)}</td>
                    <td class="plan-value">{format_value(feature['values'].get('enterprise', 'N/A'), 'enterprise', feature)}</td>
                </tr>"""

    html_content += """
            </tbody>
        </table>
    </div>

    <!-- Modal for Add Feature -->
    <div id="addFeatureModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h4>Add Missing Feature</h4>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // Store plan mapping data for JavaScript
        const planMapping = """ + json.dumps(plan_mapping) + """;
        const featureMatrix = """ + json.dumps(feature_matrix) + """;

        function showAddFeatureModal(productId, featureId, planType, featureName, groupName, subGroupName) {
            const modal = document.getElementById('addFeatureModal');
            const modalContent = document.getElementById('modalContent');

            // Find the feature in the matrix
            const feature = featureMatrix.find(f => f.id === featureId);
            if (!feature) {
                alert('Feature not found');
                return;
            }

            // Find a reference value from adjacent plans
            const planTypes = ['micro', 'small', 'medium', 'enterprise'];
            let referenceValue = null;
            let referencePlan = null;

            // Look for a value in other plans
            for (const type of planTypes) {
                if (type !== planType && feature.values[type] && feature.values[type] !== 'NO_DATA' && feature.values[type] !== 'N/A') {
                    referenceValue = feature.values[type];
                    referencePlan = type;
                    break;
                }
            }

            if (!referenceValue) {
                referenceValue = 'Yes'; // Default value
                referencePlan = 'default';
            }

            // Get the target plan ID
            const targetPlan = planMapping[planType];
            if (!targetPlan) {
                alert('Target plan not found');
                return;
            }
            console.log('log1--------------');
            // Calculate the target index for insertion
            const targetIndex = calculateTargetIndex(featureId, planType);

            console.log('log2--------------');
            // Generate MongoDB update query
            const filterQuery = `{ "product_id": ${productId}, "plan_text_id": "${planType}", "id": "${targetPlan.id}" }`;
            const pushQuery = generatePushQuery(targetPlan.id, planType, featureId, targetIndex);
            const updateQuery = generateUpdateQuery(targetPlan.id, planType, featureId, targetIndex);

            console.log('log3--------------');
            modalContent.innerHTML = `
                <p><strong>Group:</strong> ${groupName} | <strong>Subgroup:</strong> ${subGroupName || '--'}</p>
                <p><strong>Feature:</strong> ${featureName}</p>
                <p><strong>Target Plan:</strong> ${targetPlan.plan_name} (${planType}) | <strong>Reference Value:</strong> ${referenceValue} (from ${referencePlan} plan) | <strong>Target Index:</strong> ${targetIndex}</p>

                <button class="copy-btn" onclick="copyQuery('filterQueryBox')">Copy Filter Query</button>
                <div class="query-box" id="filterQueryBox">${filterQuery}</div>

                <button class="copy-btn" onclick="copyQuery('updateQueryBox')">Copy Update Query</button>
                <div class="query-box" id="updateQueryBox">${updateQuery}</div>

                <button class="copy-btn" onclick="copyQuery('pushQueryBox')">Copy Push Query</button>
                <div class="query-box" id="pushQueryBox">${pushQuery}</div>
            `;

            console.log('log4--------------');
            modal.style.display = 'block';
        }

        function calculateTargetIndex(featureId, planType) {
            // Find the feature's position in the feature matrix
            const featureIndex = featureMatrix.findIndex(f => f.id === featureId);
            if (featureIndex === -1) return 0;

            // Get the target plan
            const targetPlan = planMapping[planType];
            if (!targetPlan || !targetPlan.features) return 0;

            // Count how many features from the matrix (up to the current feature)
            // are already present in the target plan
            let targetIndex = 0;

            for (let i = 0; i < featureIndex; i++) {
                const matrixFeature = featureMatrix[i];
                const existsInPlan = targetPlan.features.some(planFeature =>
                    planFeature.id === matrixFeature.id
                );
                if (existsInPlan) {
                    targetIndex++;
                }
            }

            return targetIndex;
        }

        function generateUpdateQuery(planId, planType, featureId, targetIndex) {
            // Find the feature to get gid and sgid
            const feature = featureMatrix.find(f => f.id === featureId);

            const featureObject = {
                "gid": feature.gid,
                "gname": feature.gname,
                "sgid": feature.sgid,
                "sgname": feature.sgname,
                "id": feature.id,
                "name": feature.name,
                "is_kf": feature.is_kf,
                "is_addon": feature.is_addon,
                "addon_toggle": feature.addon_toggle,
                "is_cfg": feature.is_cfg,
                "has_bl": feature.has_bl,
                "track_usage": feature.track_usage,
                "apply_disc": feature.apply_disc,
                "ilabel": feature.ilabel,
                "ival_type": feature.ival_type,
                "ival": feature.ival
            };

            return `{
                "$set": {
                    "features.${targetIndex}.name": "${feature.name}",
                    "features.${targetIndex}.id": "${feature.id}",
                    "features.${targetIndex}.ilabel": "${feature.ilabel}",
                    "features.${targetIndex}.ival_type": "${feature.ival_type}",
                    "features.${targetIndex}.ival": "${feature.ival}"
                }
            }`;
        }

        function generatePushQuery(planId, planType, featureId, targetIndex) {
            // Find the feature to get gid and sgid
            const feature = featureMatrix.find(f => f.id === featureId);

            const featureObject = {
                "gid": feature.gid,
                "gname": feature.gname,
                "sgid": feature.sgid,
                "sgname": feature.sgname,
                "id": feature.id,
                "name": feature.name,
                "is_kf": feature.is_kf,
                "is_addon": feature.is_addon,
                "addon_toggle": feature.addon_toggle,
                "is_cfg": feature.is_cfg,
                "has_bl": feature.has_bl,
                "track_usage": feature.track_usage,
                "apply_disc": feature.apply_disc,
                "ilabel": feature.ilabel,
                "ival_type": feature.ival_type,
                "ival": feature.ival
            };

            return `{
                "$push": {
                    "features": {
                        "$each": [${JSON.stringify(featureObject, null, 16)}],
                        "$position": ${targetIndex}
                    }
                }
            }`;
        }

        function closeModal() {
            document.getElementById('addFeatureModal').style.display = 'none';
        }

        function copyQuery(elementId) {
            const queryBox = document.getElementById(elementId);
            const textArea = document.createElement('textarea');
            textArea.value = queryBox.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('Query copied to clipboard!');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('addFeatureModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>"""

    # Write to file in the parent directory
    filename = f"plans_comparison.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"HTML file generated: {filename}")
    return filename


def run(*args):
    """Main function called by Django's runscript command"""
    if len(args) != 2:
        print("Usage: python manage.py runscript generate_plans_html --script-args <product_id>")
        print("Example: python manage.py runscript generate_plans_html --script-args 2")
        return

    try:
        product_id = int(args[0])
    except ValueError:
        print("Error: Product ID must be an integer")
        return

    try:
        refer_plan = args[1]
    except IndexError:
        print("Error: missing reference plan name")
        return

    try:
        print(f"Fetching data for product ID: {product_id}")

        # Get plans
        plans = get_product_plans_and_features(product_id)

        if not plans:
            print(f"No plans found for product ID: {product_id}")
            return

        print(f"Found {len(plans)} plans")

        # Organize features by plans
        feature_matrix, plan_mapping = organize_features_by_plans(plans, refer_plan)

        # Generate HTML
        filename = generate_html(product_id, feature_matrix, plan_mapping)

        print(f"Successfully generated {filename}")
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
