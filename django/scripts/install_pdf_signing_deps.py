#!/usr/bin/env python
"""
Installation and verification script for PDF signing dependencies

This script helps install and verify the required dependencies for PDF signing.
"""

import subprocess
import sys
import importlib


def run_command(command):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def check_package_installed(package_name):
    """Check if a Python package is installed"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_dependencies():
    """Install required dependencies for PDF signing"""
    print("Installing PDF signing dependencies...")
    
    dependencies = [
        "pyhanko>=0.25.1"
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        success, output = run_command(f"pip install {dep}")
        
        if success:
            print(f"✅ Successfully installed {dep}")
        else:
            print(f"❌ Failed to install {dep}")
            print(f"Error: {output}")
            return False
    
    return True


def verify_installation():
    """Verify that all required packages are properly installed"""
    print("\nVerifying installation...")
    
    # Check basic packages
    packages_to_check = [
        ("pyhanko", "PyHanko PDF signing library"),
    ]
    
    all_good = True
    
    for package, description in packages_to_check:
        if check_package_installed(package):
            print(f"✅ {description} is installed")
        else:
            print(f"❌ {description} is NOT installed")
            all_good = False
    
    # Check specific imports
    print("\nChecking specific imports...")
    
    try:
        from pyhanko.sign import signers
        print("✅ pyhanko.sign.signers imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import pyhanko.sign.signers: {e}")
        all_good = False
    
    # Check PKCS12 loading capabilities using modern API
    try:
        from pyhanko.sign import signers
        if hasattr(signers.SimpleSigner, 'load_pkcs12'):
            print("✅ SimpleSigner.load_pkcs12 method available")
        else:
            print("❌ SimpleSigner.load_pkcs12 method not available")
            all_good = False
    except ImportError:
        print("❌ Failed to import signers module")
        all_good = False
    
    return all_good


def test_pdf_signing_script():
    """Test the PDF signing script import"""
    print("\nTesting PDF signing script...")
    
    try:
        from scripts.digitally_sign_pdf import sign_pdf_document, PDFSigningError
        print("✅ PDF signing script imported successfully")
        
        # Test with invalid input to check error handling
        result = sign_pdf_document("")
        if not result['success'] and 'PDF filename is required' in result['error']:
            print("✅ Error handling works correctly")
            return True
        else:
            print("❌ Error handling not working as expected")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import PDF signing script: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error testing PDF signing script: {e}")
        return False


def main():
    """Main function"""
    print("=" * 60)
    print("PDF Signing Dependencies Installation & Verification")
    print("=" * 60)
    
    # Check if we should install dependencies
    if len(sys.argv) > 1 and sys.argv[1] == "--install":
        if not install_dependencies():
            print("\n❌ Installation failed!")
            return 1
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Verification failed!")
        print("\nTo install dependencies, run:")
        print("python scripts/install_pdf_signing_deps.py --install")
        return 1
    
    # Test the script
    if not test_pdf_signing_script():
        print("\n❌ Script testing failed!")
        return 1
    
    print("\n🎉 All checks passed! PDF signing is ready to use.")
    print("\nYou can now use the PDF signing script:")
    print("python manage.py runscript digitally_sign_pdf --script-args document.pdf")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
