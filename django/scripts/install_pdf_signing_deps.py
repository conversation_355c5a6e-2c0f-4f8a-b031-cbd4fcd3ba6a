#!/usr/bin/env python
"""
Installation and verification script for PDF signing dependencies

This script helps install and verify the required dependencies for PDF signing.
"""

import subprocess
import sys
import importlib


def run_command(command):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr


def check_package_installed(package_name):
    """Check if a Python package is installed"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_dependencies():
    """Install required dependencies for PDF signing"""
    print("Installing PDF signing dependencies...")
    
    dependencies = [
        "pyhanko[pkcs11,image-support,opentype]==0.25.1",
        "cryptography>=3.4.8"
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        success, output = run_command(f"pip install {dep}")
        
        if success:
            print(f"✅ Successfully installed {dep}")
        else:
            print(f"❌ Failed to install {dep}")
            print(f"Error: {output}")
            return False
    
    return True


def verify_installation():
    """Verify that all required packages are properly installed"""
    print("\nVerifying installation...")
    
    # Check basic packages
    packages_to_check = [
        ("pyhanko", "PyHanko PDF signing library"),
        ("cryptography", "Cryptography library"),
    ]
    
    all_good = True
    
    for package, description in packages_to_check:
        if check_package_installed(package):
            print(f"✅ {description} is installed")
        else:
            print(f"❌ {description} is NOT installed")
            all_good = False
    
    # Check specific imports
    print("\nChecking specific imports...")
    
    try:
        from pyhanko.sign import signers
        print("✅ pyhanko.sign.signers imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import pyhanko.sign.signers: {e}")
        all_good = False
    
    # Check PKCS12 loading capabilities
    pkcs12_available = False
    
    # Try pyhanko's PKCS12 loader
    import_attempts = [
        ("pyhanko.sign.pkcs12", "load_pkcs12_keystore"),
        ("pyhanko.keys", "load_pkcs12_keystore"),
        ("pyhanko.pdf_utils.crypt", "load_pkcs12_keystore"),
    ]
    
    for module_path, function_name in import_attempts:
        try:
            module = __import__(module_path, fromlist=[function_name])
            getattr(module, function_name)
            print(f"✅ {function_name} available from {module_path}")
            pkcs12_available = True
            break
        except (ImportError, AttributeError):
            continue
    
    # Check cryptography fallback
    if not pkcs12_available:
        try:
            from cryptography.hazmat.primitives import serialization
            if hasattr(serialization, 'pkcs12') and hasattr(serialization.pkcs12, 'load_key_and_certificates'):
                print("✅ cryptography PKCS12 support available (fallback)")
                pkcs12_available = True
            else:
                print("❌ cryptography PKCS12 support not available")
        except ImportError:
            print("❌ cryptography library not available")
    
    if not pkcs12_available:
        print("❌ No PKCS12 loading method available")
        all_good = False
    
    return all_good


def test_pdf_signing_script():
    """Test the PDF signing script import"""
    print("\nTesting PDF signing script...")
    
    try:
        from scripts.digitally_sign_pdf import sign_pdf_document, PDFSigningError
        print("✅ PDF signing script imported successfully")
        
        # Test with invalid input to check error handling
        result = sign_pdf_document("")
        if not result['success'] and 'PDF filename is required' in result['error']:
            print("✅ Error handling works correctly")
            return True
        else:
            print("❌ Error handling not working as expected")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import PDF signing script: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error testing PDF signing script: {e}")
        return False


def main():
    """Main function"""
    print("=" * 60)
    print("PDF Signing Dependencies Installation & Verification")
    print("=" * 60)
    
    # Check if we should install dependencies
    if len(sys.argv) > 1 and sys.argv[1] == "--install":
        if not install_dependencies():
            print("\n❌ Installation failed!")
            return 1
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Verification failed!")
        print("\nTo install dependencies, run:")
        print("python scripts/install_pdf_signing_deps.py --install")
        return 1
    
    # Test the script
    if not test_pdf_signing_script():
        print("\n❌ Script testing failed!")
        return 1
    
    print("\n🎉 All checks passed! PDF signing is ready to use.")
    print("\nYou can now use the PDF signing script:")
    print("python manage.py runscript digitally_sign_pdf --script-args document.pdf")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
