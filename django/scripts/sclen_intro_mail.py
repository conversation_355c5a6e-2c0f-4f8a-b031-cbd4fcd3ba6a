import logging
from django.conf import settings
from utils import send_email, get_uuid
from utils.constants import <PERSON>ailHeader, AdminDBColls
from utils.mongo import MongoUtility

logger = logging.getLogger('application')

# python manage.py runscript sclen_intro_mail --script-args 1


def run(test_run=True):
    test_run = bool(int(test_run))

    db = MongoUtility()

    query = {'type': EmailHeader.INTRO.name.lower(), 'is_active': True}
    config = db.find(AdminDBColls.EMAIL_CONFIGS, query, find_one=True)
    if not config:
        return 'ERROR: Missing email config!!'

    query = {'send_email': True, 'is_bounced': False}
    receivers = db.find(AdminDBColls.INTRO_EMAIL_RECIPIENTS, query, sort=[('user_name', 1)])
    total = receivers.count()
    success_count, failed_count = 0, 0

    for i, receiver in enumerate(receivers, 1):
        user_name = receiver['user_name']
        to_email = receiver['email']
        cc = config.get('cc') or []
        bcc = config.get('bcc') or []

        if test_run:
            if i > 1:
                break
            to_email = f"{to_email.split('@')[0]}@yopmail.com"

        logger.info(f'[{i}/{total}] Initiating email for {user_name}')

        is_sent = send_email(
            receivers=[to_email],
            cc=cc,
            bcc=bcc,
            subject=config.get('subject') or EmailHeader.INTRO.value,
            message={'user_name': user_name, 'log_id': get_uuid(), 'server_url': settings.INTERNAL_SERVER_URL},
            template=config.get('template') or 'new_customer_sclen_intro.html',
            sender=config.get('from_email'),
            email_type=EmailHeader.INTRO.name,
            save_log=True
        )
        if is_sent:
            success_count += 1
        else:
            failed_count += 1

    message = f'Intro Emails Job complete: Sent - {success_count} | Failed - {failed_count}'
    logger.info(message)
    return message
