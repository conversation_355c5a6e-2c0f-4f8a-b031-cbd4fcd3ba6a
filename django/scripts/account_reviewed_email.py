import logging
from utils.constants import (
    <PERSON><PERSON><PERSON><PERSON>er,
    AppDomainID,
    AppPathID,
    AdminDBColls
)
from utils.mongo import MongoUtility
from utils import Memoization, send_email

logger = logging.getLogger('celery')


# python manage.py runscript account_reviewed_email --script-args 0 "company_id"
def run(data=None, company_id=None) -> None:
    if company_id:
        db = MongoUtility()
        data = db.find(AdminDBColls.COMPANIES, {'id': company_id}, find_one=True)

    email_template = 'account_review_status.html'
    recepient_email = data['email']
    message = {
        "status": data['acc_status'],
        "company_name": data['name'],
        "login_link": Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_LOGIN)['url'],
        "remarks": data.get('remarks')
    }
    send_email(
        [recepient_email],
        cc=[],
        bcc=[],
        subject=EmailHeader.ACCOUNT_REVIEW.value,
        message=message,
        template=email_template
    )
