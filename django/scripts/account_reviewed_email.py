import logging
from utils.constants import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    AppDomainID,
    AppPathID
)
from utils.send_email import send_email
from utils import Memoization

logger = logging.getLogger('celery')


def run(data) -> None:
    email_template = 'account_review_status.html'
    recepient_email = data['email']
    message = {
        "status": data['acc_status'],
        "company_name": data['name'],
        "login_link": Memoization.get_app_url_data(AppDomainID.AUTH, AppPathID.AUTH_LOGIN)['url'],
        "remarks": data.get('remarks')
    }
    send_email(
        [recepient_email],
        cc=[],
        bcc=[],
        subject=EmailHeader.ACCOUNT_REVIEW.value,
        message=message,
        template=email_template
    )
