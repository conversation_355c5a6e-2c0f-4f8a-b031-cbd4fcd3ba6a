import re
import json
import logging
import requests
import webbrowser
from msal import PublicClientApplication
from utils.mongo import MongoUtility
from utils.constants import AdminDBColls
from utils import DateUtil

logger = logging.getLogger('application')

CLIENT_ID = "5e5551c9-9a75-4e5e-aa51-c6664a9e7337"
TENANT_ID = "72e92a3e-e7c7-4adb-8006-a7cbe510e9e8"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = ["Mail.Read"]
FOLDER_TO_READ = 'Bounce'

# Bounced email notifications filters
PAGE_SIZE = 1000
FROM_EMAIL = "<EMAIL>"
SUBJECT_FILTER = "AWS Notification Message"
START_TIME = "2025-06-07T00:00:00Z"
END_TIME = "2025-06-07T23:59:59Z"


def get_access_token():
    logger.info('Fetching Access Token...')

    app = PublicClientApplication(client_id=CLIENT_ID, authority=AUTHORITY)

    flow = app.initiate_device_flow(scopes=SCOPES)
    webbrowser.open(flow['verification_uri'])
    logger.info(f"URI: {flow['verification_uri']} | Code: {flow['user_code']}")

    # This will open a browser popup for login
    result = app.acquire_token_by_device_flow(flow)

    if "access_token" not in result:
        logger.error(f"Error: {result.get('error')} | Desc: {result.get('error_description')}")
    return result.get("access_token")


def get_folder_id(access_token):
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    # Step 1: Find the folder ID
    folders_url = "https://graph.microsoft.com/v1.0/me/mailFolders"
    folders = requests.get(folders_url, headers=headers).json()

    bounce_folder = next(f for f in folders["value"] if f["displayName"] == FOLDER_TO_READ)
    folder_id = bounce_folder["id"]
    return folder_id


def extract_json(email_body):
    # Find the first opening `{` and the last closing `}` assuming valid JSON block is between
    match = re.search(r'({.*})', email_body, re.DOTALL)

    json_data = {}
    if match:
        json_str = match.group(1)
        try:
            json_data = json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON: {e}")
    else:
        logger.warning("No JSON found.")
    return json_data


def fetch_bounced_emails(access_token, skip=0):
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    folder_id = get_folder_id(access_token)

    url = f"https://graph.microsoft.com/v1.0/me/mailFolders/{folder_id}/messages"

    from_email_filter = f"from/emailAddress/address eq '{FROM_EMAIL}'"
    # email_subject_filter = f"contains(subject, '{SUBJECT_FILTER}')"
    time_range_filter = f"receivedDateTime ge {START_TIME} and receivedDateTime le {END_TIME}"
    params = {
        "$top": PAGE_SIZE,
        "$skip": skip,
        "$filter": " and ".join([
            from_email_filter,
            # email_subject_filter,
            time_range_filter
        ])
    }
    response = requests.get(url, headers=headers, params=params)
    next_link = None

    if response.ok:
        parsed_response = response.json()
        emails = parsed_response['value']
        next_link = parsed_response.get('@odata.nextLink')

        total = len(emails)
        for i, mail in enumerate(emails, 1):
            json_data = extract_json(mail['body']['content'])
            notif_type = json_data['notificationType']
            bounced_emails = ','.join([x['emailAddress'] for x in json_data['bounce']['bouncedRecipients']])

            logger.info(f'[{i}/{total}] notif_type: {notif_type} | bounced_emails: {bounced_emails}')

            update_bounce_data(json_data, mail)
    else:
        logger.error(f"Failed: {response.status_code} | {response.text}")

    if next_link:
        fetch_bounced_emails(access_token, skip=skip + PAGE_SIZE)


def clean_smtp_message(message: str) -> str:
    if message:
        # Remove SMTP error codes like "550-5.1.1" or "550 5.1.1"
        cleaned = re.sub(r'smtp;|(\b\d{3}[- ]\d\.\d\.\d\b)', '', message)
        # Remove multiple spaces and strip leading/trailing whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned


def update_bounce_data(data, raw_data):
    db = MongoUtility()

    bounced_recipients = data['bounce']['bouncedRecipients']
    for recipient in bounced_recipients:
        email = recipient['emailAddress'].lower()
        error_msg = clean_smtp_message(recipient.get('diagnosticCode'))
        error_data = {
            'notificationType': data['notificationType'],
            'bounceType': data['bounce']['bounceType'],
            'bounceSubType': data['bounce']['bounceSubType'],
            'action': recipient.get('action'),
            'status': recipient.get('status'),
            'diagnosticCode': recipient.get('diagnosticCode'),
            'msg': error_msg,
            'timestamp': data['bounce']['timestamp'],
        }
        update_data = {
            'error': error_data,
            'is_bounced': True,
            'updated_on': DateUtil.get_current_timestamp()
        }
        db.update(AdminDBColls.INTRO_EMAIL_RECIPIENTS, {'email': email}, update_data)


# python manage.py runscript get_bounce_emails
def run():
    access_token = get_access_token()
    fetch_bounced_emails(access_token)
