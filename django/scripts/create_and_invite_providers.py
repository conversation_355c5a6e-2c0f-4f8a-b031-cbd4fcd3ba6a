import logging
from pydantic import ValidationError
# from utils import get_uuid, DateUtil
from utils.mongo import MongoUtility
from utils.constants import AdminDBColls, UserType, UserRole, CompanyType
from masters.user_management.utils import send_new_user_invite
from schema import UserSchema

logger = logging.getLogger('application')


# python manage.py runscript create_and_invite_providers --script-args "<json_data>"
# from scripts.create_and_invite_providers import run
def run(seeker_id, data):
    """
        args = {
            'seeker_id': 'sclen',
            'data': [
                {
                    "company_id": "3dbda64242f945069ddb50013d407bf6",
                    "user_name": "Ombir",
                    "email": "<EMAIL>",
                    "phone": **********,
                }
            ]
        }
    """
    db = MongoUtility()
    # now = DateUtil.get_current_timestamp()
    total = len(data)

    for i, item in enumerate(data, 1):
        company_id = item['company_id']
        company_doc = db.find(AdminDBColls.COMPANIES, {'id': company_id}, find_one=True)
        if not company_doc:
            logger.error(f"[{i}/{total}] Company not found - company_id: {company_id}")
            continue

        doc = {
            # "id": get_uuid(),
            "is_active": True,
            "company_id": company_id,
            "company_name": company_doc['name'],
            "company_type": CompanyType.PROVIDER.value,
            "user_name": item['user_name'],
            "email": item['email'],
            "phone": item['phone'],
            "user_type": UserType.PROVIDER.value,
            "user_role": UserRole.PROVIDER.value,
            # "pwd_hash": None,
            # "pwd_changed_on": None,
            # "pwd_history": [],
            # "email_verified_on": None,
            # "created_on": now,
            # "updated_on": now
        }

        try:
            validated_doc = UserSchema(**doc).model_dump()
        except ValidationError as e:
            logger.error(f"[{i}/{total}] Invalid data - {str(e)}")
            continue

        query = {'email': doc['email']}
        existing_doc = db.find(AdminDBColls.USERS, query, find_one=True)
        if existing_doc:
            logger.warning(f"[{i}/{total}] User with email - {doc['email']} already exists. Skipping")
        else:
            logger.info(f"[{i}/{total}] Created user with email - {doc['email']}")
            db.insert(AdminDBColls.USERS, [validated_doc])

        if not existing_doc.get('email_verified_on'):
            user_query = {
                'company_id': seeker_id,
                'company_type': CompanyType.SEEKER.value,
                'user_role': UserRole.ADMIN_SEEKER.value
            }
            admin_user = db.find(AdminDBColls.USERS, user_query, find_one=True)
            if not admin_user:
                logger.error('Admin user not found. Exiting!!')
                return
            send_new_user_invite(validated_doc, admin_user)
