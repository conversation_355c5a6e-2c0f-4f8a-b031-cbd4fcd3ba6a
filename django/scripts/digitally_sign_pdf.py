import os
import logging
from django.conf import settings

logger = logging.getLogger('application')


class PDFSigningError(Exception):
    """Custom exception for PDF signing errors"""
    pass


def validate_inputs(pdf_filename, pfx_file_path, pfx_password):
    """Validate input parameters and file existence"""
    if not pdf_filename:
        raise PDFSigningError("PDF filename is required")

    if not pdf_filename.lower().endswith('.pdf'):
        raise PDFSigningError("File must be a PDF")

    if not pfx_file_path:
        raise PDFSigningError("Certificate file path is required")

    if not pfx_password:
        raise PDFSigningError("Certificate password is required")

    # Check if certificate file exists
    if not os.path.exists(pfx_file_path):
        raise PDFSigningError(f"Certificate file not found: {pfx_file_path}")

    # Check if input PDF exists
    input_pdf = os.path.join(settings.MEDIA_ROOT, pdf_filename)
    if not os.path.exists(input_pdf):
        raise PDFSigningError(f"Input PDF file not found: {input_pdf}")

    return input_pdf


def sign_pdf_document(pdf_filename, pfx_file_path=None, pfx_password=None, signature_field_name="Signature1"):
    """
    Digitally sign a PDF document using a PKCS#12 certificate

    Args:
        pdf_filename (str): Name of the PDF file to sign (must be in MEDIA_ROOT)
        pfx_file_path (str, optional): Path to the PKCS#12 certificate file
        pfx_password (str, optional): Password for the certificate file
        signature_field_name (str, optional): Name for the signature field

    Returns:
        dict: Result containing success status, output path, and any error messages
    """
    try:
        # Import pyhanko here to provide better error message if not installed
        try:
            from pyhanko.sign import signers
            from pyhanko.sign.pkcs12 import load_pkcs12_keystore
        except ImportError as e:
            raise PDFSigningError(
                "pyhanko library is not installed. Please add 'pyhanko' to requirements.txt and install it."
            ) from e

        # Use default certificate if not provided
        if not pfx_file_path:
            pfx_file_path = os.path.join(settings.MEDIA_ROOT, 'DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx')

        if not pfx_password:
            pfx_password = getattr(settings, 'PDF_SIGNING_CERT_PASSWORD', '12345678')

        # Convert password to bytes if it's a string
        if isinstance(pfx_password, str):
            pfx_password = pfx_password.encode('utf-8')

        # Validate inputs
        input_pdf = validate_inputs(pdf_filename, pfx_file_path, pfx_password)

        # Generate output filename
        base_name, ext = os.path.splitext(pdf_filename)
        output_filename = f'signed_{base_name}{ext}'
        output_pdf = os.path.join(settings.MEDIA_ROOT, output_filename)

        logger.info(f'Starting PDF signing process for: {pdf_filename}')

        # Load certificate and key
        try:
            with open(pfx_file_path, "rb") as f:
                key, cert, additional_certs = load_pkcs12_keystore(f.read(), pfx_password)
        except Exception as e:
            raise PDFSigningError(f"Failed to load certificate: {str(e)}") from e

        # Create signer
        try:
            signer = signers.SimpleSigner(
                signing_cert=cert,
                signing_key=key,
                cert_registry=signers.SimpleCertificateStore(additional_certs),
                signature_mechanism=signers.PdfSignatureMetadata(field_name=signature_field_name)
            )
        except Exception as e:
            raise PDFSigningError(f"Failed to create signer: {str(e)}") from e

        # Sign the PDF
        try:
            with open(input_pdf, "rb") as inf, open(output_pdf, "wb") as outf:
                signers.sign_pdf(inf, signer=signer, output=outf)
        except Exception as e:
            # Clean up partial output file if it exists
            if os.path.exists(output_pdf):
                try:
                    os.remove(output_pdf)
                except:
                    pass
            raise PDFSigningError(f"Failed to sign PDF: {str(e)}") from e

        logger.info(f'Successfully signed PDF. Output stored at: {output_pdf}')

        return {
            'success': True,
            'input_file': input_pdf,
            'output_file': output_pdf,
            'output_filename': output_filename,
            'message': 'PDF signed successfully'
        }

    except PDFSigningError as e:
        logger.error(f'PDF signing failed: {str(e)}')
        return {
            'success': False,
            'error': str(e),
            'message': 'PDF signing failed'
        }
    except Exception as e:
        logger.error(f'Unexpected error during PDF signing: {str(e)}')
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'message': 'PDF signing failed due to unexpected error'
        }


# python manage.py runscript digitally_sign_pdf --script-args filename.pdf
def run(*args):
    """
    Main function called by Django's runscript command

    Usage:
        python manage.py runscript digitally_sign_pdf --script-args filename.pdf
        python manage.py runscript digitally_sign_pdf --script-args filename.pdf /path/to/cert.pfx password
    """
    # if not args:
    #     print("Usage: python manage.py runscript digitally_sign_pdf --script-args <pdf_filename> [cert_path] [cert_password]")
    #     print("Example: python manage.py runscript digitally_sign_pdf --script-args document.pdf")
    #     return

    # pdf_filename = args[0]
    # pfx_file_path = args[1] if len(args) > 1 else None
    # pfx_password = args[2] if len(args) > 2 else None

    pdf_filename = os.path.join(settings.MEDIA_ROOT, 'dgfc.pdf')
    pfx_file_path = os.path.join(settings.MEDIA_ROOT, 'DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx')
    pfx_password = '12345678'

    result = sign_pdf_document(pdf_filename, pfx_file_path, pfx_password)

    if result['success']:
        print(f"✅ {result['message']}")
        print(f"📄 Input: {result['input_file']}")
        print(f"📄 Output: {result['output_file']}")
    else:
        print(f"❌ {result['message']}")
        print(f"🔍 Error: {result['error']}")
        return 1  # Exit with error code

    return 0
