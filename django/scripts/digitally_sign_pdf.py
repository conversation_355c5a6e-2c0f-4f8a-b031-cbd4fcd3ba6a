import os
import logging
import fitz  # PyMuPDF
from django.conf import settings
from pyhanko.sign import signers
from pyhanko.sign.fields import SigFieldSpec
from pyhanko.pdf_utils.incremental_writer import IncrementalPdfFileWriter

logger = logging.getLogger('application')


def get_signature_box(pdf_path, width=150, height=50):
    """Get box coordinates for bottom-right corner on last page."""
    doc = fitz.open(pdf_path)
    last_page = doc[-1]
    page_width = last_page.rect.width
    # page_height = last_page.rect.height
    doc.close()

    # bottom-right box (llx, lly, urx, ury)
    return (
        page_width - width - 20,   # llx: shift left
        20,                        # lly: 20 pts from bottom
        page_width - 20,           # urx: right edge - 20
        20 + height                # ury: bottom + height
    )


# python manage.py runscript digitally_sign_pdf
def run(*args):
    pdf_filename = "dgfc.pdf"
    pfx_file_path = os.path.join(settings.MEDIA_ROOT, "DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx")
    pfx_password = "12345678"

    input_file_path = os.path.join(settings.MEDIA_ROOT, pdf_filename)
    output_file_path = os.path.join(settings.MEDIA_ROOT, f'signed_{pdf_filename}')

    signature_field_name = "Signature1"
    signature_box = get_signature_box(input_file_path)  # (x1, y1, x2, y2) in points
    page_num = -1

    signer = signers.SimpleSigner.load_pkcs12(
        pfx_file=pfx_file_path,
        passphrase=pfx_password.encode('utf-8')
    )

    with open(input_file_path, 'rb') as inf:
        writer = IncrementalPdfFileWriter(inf)

        # Create signature metadata
        signature_meta = signers.PdfSignatureMetadata(
            field_name=signature_field_name,
        )

        # Create signature field specification with proper positioning
        sig_field_spec = SigFieldSpec(
            sig_field_name=signature_field_name,
            on_page=page_num,
            box=signature_box
        )

        # Sign the PDF using the correct API
        out = signers.sign_pdf(
            pdf_out=writer,
            signature_meta=signature_meta,
            signer=signer,
            new_field_spec=sig_field_spec
        )

        # Write the signed PDF to output file
        with open(output_file_path, 'wb') as outf:
            outf.write(out.getbuffer())

    logger.info(f"✅ PDF signed successfully: {output_file_path}")
    return
