import os
import logging
from django.conf import settings

logger = logging.getLogger('application')


class PDFSigningError(Exception):
    """Custom exception for PDF signing errors"""
    pass


def validate_inputs(pdf_filename, pfx_file_path, pfx_password):
    """Validate input parameters and file existence"""
    if not pdf_filename:
        raise PDFSigningError("PDF filename is required")

    if not pdf_filename.lower().endswith('.pdf'):
        raise PDFSigningError("File must be a PDF")

    if not pfx_file_path:
        raise PDFSigningError("Certificate file path is required")

    if not pfx_password:
        raise PDFSigningError("Certificate password is required")

    # Check if certificate file exists
    if not os.path.exists(pfx_file_path):
        raise PDFSigningError(f"Certificate file not found: {pfx_file_path}")

    # Check if input PDF exists
    input_pdf = os.path.join(settings.MEDIA_ROOT, pdf_filename)
    if not os.path.exists(input_pdf):
        raise PDFSigningError(f"Input PDF file not found: {input_pdf}")

    return input_pdf


def sign_pdf_document(pdf_filename, pfx_file_path=None, pfx_password=None, signature_field_name="Signature1"):
    """
    Digitally sign a PDF document using a PKCS#12 certificate

    Args:
        pdf_filename (str): Name of the PDF file to sign (must be in MEDIA_ROOT)
        pfx_file_path (str, optional): Path to the PKCS#12 certificate file
        pfx_password (str, optional): Password for the certificate file
        signature_field_name (str, optional): Name for the signature field

    Returns:
        dict: Result containing success status, output path, and any error messages
    """
    try:
        # Import pyhanko here to provide better error message if not installed
        try:
            from pyhanko.sign import signers
        except ImportError as e:
            raise PDFSigningError(
                "pyhanko library is not installed or has missing dependencies. "
                "Please install with: pip install pyhanko[pkcs11,image-support,opentype] cryptography>=3.4.8"
            ) from e

        # Try different import paths for PKCS12 loading across different pyhanko versions
        load_pkcs12_keystore = None
        import_attempts = [
            ("pyhanko.sign.pkcs12", "load_pkcs12_keystore"),
            ("pyhanko.keys", "load_pkcs12_keystore"),
            ("pyhanko.pdf_utils.crypt", "load_pkcs12_keystore"),
        ]

        for module_path, function_name in import_attempts:
            try:
                module = __import__(module_path, fromlist=[function_name])
                load_pkcs12_keystore = getattr(module, function_name)
                logger.debug(f"Successfully imported {function_name} from {module_path}")
                break
            except (ImportError, AttributeError):
                continue

        # If pyhanko's PKCS12 loader is not available, ensure cryptography is available
        if load_pkcs12_keystore is None:
            try:
                from cryptography.hazmat.primitives import serialization
                if not hasattr(serialization, 'pkcs12') or not hasattr(serialization.pkcs12, 'load_key_and_certificates'):
                    raise PDFSigningError(
                        "Neither pyhanko PKCS12 loader nor cryptography PKCS12 support is available. "
                        "Please update your cryptography library: pip install cryptography>=3.4.8"
                    )
                logger.debug("Using cryptography library for PKCS12 loading")
            except ImportError as e:
                raise PDFSigningError(
                    "cryptography library is required but not available. "
                    "Please install with: pip install cryptography>=3.4.8"
                ) from e

        # Use default certificate if not provided
        if not pfx_file_path:
            pfx_file_path = os.path.join(settings.MEDIA_ROOT, 'DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx')

        if not pfx_password:
            pfx_password = getattr(settings, 'PDF_SIGNING_CERT_PASSWORD', '12345678')

        # Convert password to bytes if it's a string
        if isinstance(pfx_password, str):
            pfx_password = pfx_password.encode('utf-8')

        # Validate inputs
        input_pdf = validate_inputs(pdf_filename, pfx_file_path, pfx_password)

        # Generate output filename
        base_name, ext = os.path.splitext(pdf_filename)
        output_filename = f'signed_{base_name}{ext}'
        output_pdf = os.path.join(settings.MEDIA_ROOT, output_filename)

        logger.info(f'Starting PDF signing process for: {pdf_filename}')

        # Load certificate and key
        try:
            with open(pfx_file_path, "rb") as f:
                pfx_data = f.read()

            if load_pkcs12_keystore is not None:
                # Use pyhanko's built-in function
                key, cert, additional_certs = load_pkcs12_keystore(pfx_data, pfx_password)
            else:
                # Use cryptography library directly for newer versions
                from cryptography.hazmat.primitives import serialization
                from cryptography import x509

                # Load PKCS12 using cryptography
                private_key, certificate, additional_certificates = serialization.pkcs12.load_key_and_certificates(
                    pfx_data, pfx_password
                )

                # Convert to pyhanko format
                key = private_key
                cert = certificate
                additional_certs = additional_certificates or []

        except Exception as e:
            raise PDFSigningError(f"Failed to load certificate: {str(e)}") from e

        # Create signer
        try:
            signer = signers.SimpleSigner(
                signing_cert=cert,
                signing_key=key,
                cert_registry=signers.SimpleCertificateStore(additional_certs),
                signature_mechanism=signers.PdfSignatureMetadata(field_name=signature_field_name)
            )
        except Exception as e:
            raise PDFSigningError(f"Failed to create signer: {str(e)}") from e

        # Sign the PDF
        try:
            with open(input_pdf, "rb") as inf, open(output_pdf, "wb") as outf:
                signers.sign_pdf(inf, signer=signer, output=outf)
        except Exception as e:
            # Clean up partial output file if it exists
            if os.path.exists(output_pdf):
                try:
                    os.remove(output_pdf)
                except:
                    pass
            raise PDFSigningError(f"Failed to sign PDF: {str(e)}") from e

        logger.info(f'Successfully signed PDF. Output stored at: {output_pdf}')

        return {
            'success': True,
            'input_file': input_pdf,
            'output_file': output_pdf,
            'output_filename': output_filename,
            'message': 'PDF signed successfully'
        }

    except PDFSigningError as e:
        logger.error(f'PDF signing failed: {str(e)}')
        return {
            'success': False,
            'error': str(e),
            'message': 'PDF signing failed'
        }
    except Exception as e:
        logger.error(f'Unexpected error during PDF signing: {str(e)}')
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'message': 'PDF signing failed due to unexpected error'
        }


# python manage.py runscript digitally_sign_pdf --script-args filename.pdf
def run(*args):
    """
    Main function called by Django's runscript command

    Usage:
        python manage.py runscript digitally_sign_pdf --script-args filename.pdf
        python manage.py runscript digitally_sign_pdf --script-args filename.pdf /path/to/cert.pfx password
    """
    # if not args:
    #     print("Usage: python manage.py runscript digitally_sign_pdf --script-args <pdf_filename> [cert_path] [cert_password]")
    #     print("Example: python manage.py runscript digitally_sign_pdf --script-args document.pdf")
    #     return

    # pdf_filename = args[0]
    # pfx_file_path = args[1] if len(args) > 1 else None
    # pfx_password = args[2] if len(args) > 2 else None

    pdf_filename = os.path.join(settings.MEDIA_ROOT, 'dgfc.pdf')
    pfx_file_path = os.path.join(settings.MEDIA_ROOT, 'DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx')
    pfx_password = '12345678'

    result = sign_pdf_document(pdf_filename, pfx_file_path, pfx_password)

    if result['success']:
        print(f"✅ {result['message']}")
        print(f"📄 Input: {result['input_file']}")
        print(f"📄 Output: {result['output_file']}")
    else:
        print(f"❌ {result['message']}")
        print(f"🔍 Error: {result['error']}")
        return 1  # Exit with error code

    return 0
