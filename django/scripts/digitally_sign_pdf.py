import os
import logging
from django.conf import settings
from pyhanko.sign import signers
from pyhanko.sign.pkcs12 import load_pkcs12_keystore

logger = logging.getLogger('application')


# python manage.py runscript digitally_sign_pdf
def run(pdf_filename):
    # Setup signer
    pfx_file = os.path.join(settings.MEDIA_ROOT, 'DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx')
    pfx_password = b"12345678"

    # PDF input/output
    input_pdf = os.path.join(settings.MEDIA_ROOT, pdf_filename)
    output_pdf = os.path.join(settings.MEDIA_ROOT, f'signed_{pdf_filename}')

    with open(pfx_file, "rb") as f:
        key, cert, additional_certs = load_pkcs12_keystore(f.read(), pfx_password)

    signer = signers.SimpleSigner(
        signing_cert=cert,
        signing_key=key,
        cert_registry=signers.SimpleCertificateStore(additional_certs),
        signature_mechanism=signers.PdfSignatureMetadata(field_name="Signature1")
    )

    with open(input_pdf, "rb") as inf, open(output_pdf, "wb") as outf:
        signers.sign_pdf(inf, signer=signer, output=outf)

    logger.info(f'Digitally signed pdf stored at: {output_pdf}')
    return
