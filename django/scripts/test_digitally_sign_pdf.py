#!/usr/bin/env python
"""
Test script for digitally_sign_pdf.py

This script tests the PDF signing functionality without requiring actual certificates.
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch, mock_open, MagicMock

# Add the django directory to the path so we can import the script
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock Django settings
class MockSettings:
    MEDIA_ROOT = '/tmp/test_media'
    PDF_SIGNING_CERT_PASSWORD = 'test_password'

# Mock Django
sys.modules['django'] = MagicMock()
sys.modules['django.conf'] = MagicMock()
sys.modules['django.conf'].settings = MockSettings()

# Mock the PDF utilities that will be imported
sys.modules['pyhanko.pdf_utils.incremental_writer'] = MagicMock()
sys.modules['pyhanko.pdf_utils.reader'] = MagicMock()

# Now import our script
from scripts.digitally_sign_pdf import sign_pdf_document, validate_inputs, PDFSigningError


class TestDigitallySignPDF(unittest.TestCase):
    
    def setUp(self):
        self.test_media_root = '/tmp/test_media'
        self.test_pdf_filename = 'test_document.pdf'
        self.test_cert_path = '/tmp/test_cert.pfx'
        self.test_cert_password = 'test_password'
    
    def test_validate_inputs_success(self):
        """Test successful input validation"""
        with patch('os.path.exists', return_value=True):
            result = validate_inputs(
                self.test_pdf_filename,
                self.test_cert_path,
                self.test_cert_password
            )
            expected_path = os.path.join(self.test_media_root, self.test_pdf_filename)
            self.assertEqual(result, expected_path)
    
    def test_validate_inputs_empty_filename(self):
        """Test validation with empty filename"""
        with self.assertRaises(PDFSigningError) as context:
            validate_inputs('', self.test_cert_path, self.test_cert_password)
        self.assertIn('PDF filename is required', str(context.exception))
    
    def test_validate_inputs_non_pdf_file(self):
        """Test validation with non-PDF file"""
        with self.assertRaises(PDFSigningError) as context:
            validate_inputs('test.txt', self.test_cert_path, self.test_cert_password)
        self.assertIn('File must be a PDF', str(context.exception))
    
    def test_validate_inputs_missing_cert_file(self):
        """Test validation with missing certificate file"""
        with patch('os.path.exists', side_effect=lambda path: path.endswith('.pdf')):
            with self.assertRaises(PDFSigningError) as context:
                validate_inputs(
                    self.test_pdf_filename,
                    self.test_cert_path,
                    self.test_cert_password
                )
            self.assertIn('Certificate file not found', str(context.exception))
    
    def test_validate_inputs_missing_pdf_file(self):
        """Test validation with missing PDF file"""
        with patch('os.path.exists', side_effect=lambda path: path.endswith('.pfx')):
            with self.assertRaises(PDFSigningError) as context:
                validate_inputs(
                    self.test_pdf_filename,
                    self.test_cert_path,
                    self.test_cert_password
                )
            self.assertIn('Input PDF file not found', str(context.exception))
    
    def test_sign_pdf_document_missing_pyhanko(self):
        """Test PDF signing when pyhanko is not installed"""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'pyhanko'")):
            result = sign_pdf_document(self.test_pdf_filename)
            self.assertFalse(result['success'])
            self.assertIn('pyhanko library is not installed', result['error'])
    
    @patch('scripts.digitally_sign_pdf.signers')
    @patch('scripts.digitally_sign_pdf.PdfFileReader')
    @patch('scripts.digitally_sign_pdf.IncrementalPdfFileWriter')
    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', new_callable=mock_open, read_data=b'fake_pdf_content')
    def test_sign_pdf_document_success(self, mock_file, mock_exists, mock_writer, mock_reader, mock_signers):
        """Test successful PDF signing"""
        # Mock the SimpleSigner.load_pkcs12 method
        mock_signer = MagicMock()
        mock_signers.SimpleSigner.load_pkcs12.return_value = mock_signer
        mock_signers.PdfSignatureMetadata.return_value = MagicMock()
        mock_signers.sign_pdf = MagicMock()

        # Mock PDF reader and writer
        mock_reader.return_value = MagicMock()
        mock_writer.return_value = MagicMock()

        result = sign_pdf_document(
            self.test_pdf_filename,
            self.test_cert_path,
            self.test_cert_password
        )

        self.assertTrue(result['success'])
        self.assertEqual(result['message'], 'PDF signed successfully')
        self.assertIn('signed_test_document.pdf', result['output_filename'])

        # Verify that the correct methods were called
        mock_signers.SimpleSigner.load_pkcs12.assert_called_once_with(
            pfx_file=self.test_cert_path,
            passphrase=self.test_cert_password.encode('utf-8')
        )
    
    @patch('os.path.exists', return_value=True)
    @patch('builtins.open', side_effect=FileNotFoundError("Certificate file not found"))
    def test_sign_pdf_document_cert_load_failure(self, mock_file, mock_exists):
        """Test PDF signing when certificate loading fails"""
        result = sign_pdf_document(
            self.test_pdf_filename,
            self.test_cert_path,
            self.test_cert_password
        )
        
        self.assertFalse(result['success'])
        self.assertIn('Failed to load certificate', result['error'])


def run_tests():
    """Run the test suite"""
    unittest.main(verbosity=2)


if __name__ == '__main__':
    run_tests()
