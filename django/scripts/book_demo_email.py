import logging
from django.conf import settings
from utils.constants import <PERSON><PERSON><PERSON><PERSON><PERSON>, SAASProduct
from utils import send_email

logger = logging.getLogger('celery')


def run(data) -> None:
    email_template = 'book_a_demo.html'
    recepient_email = settings.EMAIL_RECEPIENT_DEFAULT
    products = [SAASProduct(pid).name for pid in data.get('product_ids', [])]
    message = {
        'products': ', '.join(products),
        'name': ' '.join([data['first_name'], data['last_name']]),
        'email': data['email'],
        'contact': data['phone'],
        'company_name': data['company_name'],
        'message': data['message'],
        'created_on': data['created_on']
    }
    send_email(
        [recepient_email],
        cc=[],
        bcc=[],
        subject=EmailHeader.BOOK_A_DEMO.value,
        message=message,
        template=email_template
    )
