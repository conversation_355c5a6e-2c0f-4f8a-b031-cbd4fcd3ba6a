import logging
from utils.constants import AdminDBColls
from utils.mongo import MongoUtility
from utils import (
    make_currency_vs_rates,
    get_currency_rates,
    DateUtil,
)

logger = logging.getLogger('celery')


def run():
    db = MongoUtility()
    error_message = None
    now = DateUtil.get_current_timestamp()
    created_on = DateUtil.convert_to_datetime(now)
    created_on = DateUtil.format_timestamp(created_on, str_format='%Y%m%d')
    created_on = int(created_on)
    query = {'created_on': created_on}
    currency_vs_rates = db.find(AdminDBColls.CURRENCY_VS_RATES, query, find_one=True)
    if not currency_vs_rates:
        response, success = get_currency_rates()
        if not success:
            error_message = "Exchange rates not available at the moment"
        else:
            currency_vs_rates = make_currency_vs_rates(response['quotes'], created_on)
            db.insert(AdminDBColls.CURRENCY_VS_RATES, [currency_vs_rates])

    message = "Sync currency rates job completed"
    if error_message:
        message = ''.join([message, " | ", error_message])

    logger.info(message)
    return message
