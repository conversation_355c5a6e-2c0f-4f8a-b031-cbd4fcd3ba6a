#!/usr/bin/env python
"""
Test script to verify pyhanko imports work correctly

This script tests different import paths for pyhanko to ensure compatibility
across different versions.
"""

import sys


def test_pyhanko_imports():
    """Test different pyhanko import paths"""
    print("Testing pyhanko imports...")
    
    # Test basic pyhanko import
    try:
        import pyhanko
        print(f"✅ pyhanko version: {pyhanko.__version__}")
    except ImportError as e:
        print(f"❌ Failed to import pyhanko: {e}")
        return False
    
    # Test signers import
    try:
        from pyhanko.sign import signers
        print("✅ pyhanko.sign.signers imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import signers: {e}")
        return False
    
    # Test PKCS12 import with fallbacks
    pkcs12_imported = False
    load_pkcs12_keystore = None
    
    # Try different import paths
    import_attempts = [
        ("pyhanko.sign.pkcs12", "load_pkcs12_keystore"),
        ("pyhanko.keys", "load_pkcs12_keystore"),
        ("pyhanko.pdf_utils.crypt", "load_pkcs12_keystore"),
    ]
    
    for module_path, function_name in import_attempts:
        try:
            module = __import__(module_path, fromlist=[function_name])
            load_pkcs12_keystore = getattr(module, function_name)
            print(f"✅ {function_name} imported from {module_path}")
            pkcs12_imported = True
            break
        except (ImportError, AttributeError) as e:
            print(f"⚠️  Failed to import {function_name} from {module_path}: {e}")
    
    # Test cryptography fallback
    if not pkcs12_imported:
        try:
            from cryptography.hazmat.primitives import serialization
            print("✅ cryptography.hazmat.primitives.serialization imported (fallback)")
            
            # Test PKCS12 loading function
            if hasattr(serialization, 'pkcs12'):
                print("✅ cryptography PKCS12 support available")
                pkcs12_imported = True
            else:
                print("❌ cryptography PKCS12 support not available")
        except ImportError as e:
            print(f"❌ Failed to import cryptography fallback: {e}")
    
    if not pkcs12_imported:
        print("❌ No PKCS12 loading method available")
        return False
    
    # Test other required components
    try:
        signers.SimpleSigner
        print("✅ SimpleSigner class available")
    except AttributeError as e:
        print(f"❌ SimpleSigner not available: {e}")
        return False
    
    try:
        signers.SimpleCertificateStore
        print("✅ SimpleCertificateStore class available")
    except AttributeError as e:
        print(f"❌ SimpleCertificateStore not available: {e}")
        return False
    
    try:
        signers.PdfSignatureMetadata
        print("✅ PdfSignatureMetadata class available")
    except AttributeError as e:
        print(f"❌ PdfSignatureMetadata not available: {e}")
        return False
    
    try:
        signers.sign_pdf
        print("✅ sign_pdf function available")
    except AttributeError as e:
        print(f"❌ sign_pdf function not available: {e}")
        return False
    
    print("\n🎉 All pyhanko imports successful!")
    return True


def test_cryptography_pkcs12():
    """Test cryptography PKCS12 functionality"""
    print("\nTesting cryptography PKCS12 functionality...")
    
    try:
        from cryptography.hazmat.primitives import serialization
        
        # Check if PKCS12 loading is available
        if hasattr(serialization, 'pkcs12'):
            print("✅ cryptography PKCS12 module available")
            
            # Check if the load function exists
            if hasattr(serialization.pkcs12, 'load_key_and_certificates'):
                print("✅ load_key_and_certificates function available")
                return True
            else:
                print("❌ load_key_and_certificates function not available")
                return False
        else:
            print("❌ cryptography PKCS12 module not available")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import cryptography: {e}")
        return False


def main():
    """Main test function"""
    print("=" * 60)
    print("PyHanko Import Compatibility Test")
    print("=" * 60)
    
    success = True
    
    # Test pyhanko imports
    if not test_pyhanko_imports():
        success = False
    
    # Test cryptography fallback
    if not test_cryptography_pkcs12():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! PDF signing should work correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check your pyhanko installation.")
        print("\nTry installing with:")
        print("pip install pyhanko[pkcs11,image-support,opentype]")
        print("pip install cryptography>=3.4.8")
        return 1


if __name__ == "__main__":
    sys.exit(main())
