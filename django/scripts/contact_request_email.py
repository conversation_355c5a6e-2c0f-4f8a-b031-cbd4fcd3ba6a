import logging
from utils import send_email
from django.conf import settings
from utils.constants import EmailHeader

logger = logging.getLogger('application')


def run(data: dict) -> None:
    email_template = 'contact_request.html'
    recepient_email = settings.EMAIL_RECEPIENT_DEFAULT
    send_email(
        [recepient_email],
        cc=[],
        bcc=[],
        subject=EmailHeader.CONTACT_REQUEST.value,
        message=data,
        template=email_template
    )
