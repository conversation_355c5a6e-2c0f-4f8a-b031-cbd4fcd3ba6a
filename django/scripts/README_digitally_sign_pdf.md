# Digital PDF Signing Script

This script provides functionality to digitally sign PDF documents using PKCS#12 certificates.

## Features

- ✅ **Robust Error Handling**: Comprehensive error handling with custom exceptions
- ✅ **Input Validation**: Validates PDF files, certificate files, and passwords
- ✅ **Configurable**: Supports custom certificate paths, passwords, and signature field names
- ✅ **Security**: Supports environment-based configuration for sensitive data
- ✅ **Logging**: Detailed logging for debugging and monitoring
- ✅ **Return Values**: Returns structured results for programmatic use

## Dependencies

The script requires the `pyhanko` library for PDF signing functionality:

```bash
pip install pyhanko==0.25.1
```

This dependency has been added to `requirements.txt`.

## Usage

### Command Line Usage

```bash
# Basic usage with default certificate
python manage.py runscript digitally_sign_pdf --script-args document.pdf

# With custom certificate and password
python manage.py runscript digitally_sign_pdf --script-args document.pdf /path/to/cert.pfx mypassword
```

### Programmatic Usage

```python
from scripts.digitally_sign_pdf import sign_pdf_document

# Basic usage
result = sign_pdf_document('document.pdf')

# With custom parameters
result = sign_pdf_document(
    pdf_filename='document.pdf',
    pfx_file_path='/path/to/certificate.pfx',
    pfx_password='certificate_password',
    signature_field_name='MySignature'
)

if result['success']:
    print(f"PDF signed successfully: {result['output_file']}")
else:
    print(f"Signing failed: {result['error']}")
```

## Configuration

### Environment Variables

You can configure the default certificate password using Django settings:

```python
# In settings.py
PDF_SIGNING_CERT_PASSWORD = 'your_certificate_password'
```

### Default Certificate Location

By default, the script looks for the certificate file at:
```
{MEDIA_ROOT}/DS DELHI GUJARAT FLEET CARRIERS PVT.LTD. 3.pfx
```

## File Structure

- **Input**: PDF files must be located in `MEDIA_ROOT`
- **Output**: Signed PDFs are saved in `MEDIA_ROOT` with `signed_` prefix
- **Certificate**: PKCS#12 (.pfx) certificate file

## Return Format

The `sign_pdf_document` function returns a dictionary with the following structure:

### Success Response
```python
{
    'success': True,
    'input_file': '/path/to/input.pdf',
    'output_file': '/path/to/signed_input.pdf',
    'output_filename': 'signed_input.pdf',
    'message': 'PDF signed successfully'
}
```

### Error Response
```python
{
    'success': False,
    'error': 'Error description',
    'message': 'PDF signing failed'
}
```

## Error Handling

The script handles various error scenarios:

- **Missing Dependencies**: Clear message if pyhanko is not installed
- **File Not Found**: Validates existence of PDF and certificate files
- **Invalid Input**: Validates PDF file extension and required parameters
- **Certificate Issues**: Handles certificate loading and validation errors
- **Signing Failures**: Catches and reports PDF signing errors

## Security Considerations

1. **Certificate Storage**: Store certificates securely and limit access
2. **Password Management**: Use environment variables for passwords
3. **File Permissions**: Ensure proper file permissions for certificates and PDFs
4. **Cleanup**: Failed signing attempts clean up partial output files

## Testing

Run the test suite to verify functionality:

```bash
python django/scripts/test_digitally_sign_pdf.py
```

## Troubleshooting

### Common Issues

1. **ImportError: No module named 'pyhanko'**
   - Solution: Install pyhanko using `pip install pyhanko==0.25.1`

2. **Certificate file not found**
   - Solution: Ensure the certificate file exists at the specified path

3. **Failed to load certificate**
   - Solution: Verify the certificate password and file format (must be PKCS#12)

4. **Input PDF file not found**
   - Solution: Ensure the PDF file exists in the MEDIA_ROOT directory

### Logging

The script uses Django's logging system with the 'application' logger. Enable debug logging to see detailed information:

```python
LOGGING = {
    'loggers': {
        'application': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

## Examples

### Example 1: Basic Signing
```bash
python manage.py runscript digitally_sign_pdf --script-args invoice.pdf
```

### Example 2: Custom Certificate
```bash
python manage.py runscript digitally_sign_pdf --script-args contract.pdf /secure/certs/company.pfx secretpassword
```

### Example 3: Programmatic Integration
```python
def process_document(pdf_name):
    result = sign_pdf_document(pdf_name)
    
    if result['success']:
        # Send signed document via email
        send_signed_document(result['output_file'])
        return True
    else:
        # Log error and notify admin
        logger.error(f"Failed to sign {pdf_name}: {result['error']}")
        notify_admin(result['error'])
        return False
```
