#!/usr/bin/env python
"""
Example showing the correct pyhanko API usage

This example demonstrates the modern pyhanko API for PDF signing,
which is what the fixed digitally_sign_pdf.py script now uses.
"""

def modern_pyhanko_example():
    """
    Example of modern pyhanko API usage (what the script now uses)
    """
    print("Modern PyHanko API Example:")
    print("=" * 40)
    
    example_code = '''
from pyhanko.sign import signers
from pyhanko.pdf_utils.incremental_writer import IncrementalPdfFileWriter
from pyhanko.pdf_utils.reader import PdfFileReader

# 1. Load certificate using SimpleSigner.load_pkcs12 (modern API)
signer = signers.SimpleSigner.load_pkcs12(
    pfx_file='/path/to/certificate.pfx',
    passphrase=b'certificate_password'
)

# 2. Open PDF files
with open('input.pdf', 'rb') as inf, open('signed_output.pdf', 'wb') as outf:
    # Read the PDF
    pdf_reader = PdfFileReader(inf)
    
    # Create incremental writer for signing
    writer = IncrementalPdfFileWriter(outf, pdf_reader)
    
    # Create signature field specification
    sig_field_spec = signers.PdfSignatureMetadata(field_name='Signature1')
    
    # Sign the PDF
    signers.sign_pdf(writer, sig_field_spec, signer)

print("PDF signed successfully!")
'''
    
    print(example_code)


def old_vs_new_api():
    """
    Comparison of old vs new pyhanko API
    """
    print("\nOLD vs NEW PyHanko API:")
    print("=" * 40)
    
    print("❌ OLD API (doesn't work in newer versions):")
    old_api = '''
# This approach is deprecated/removed:
from pyhanko.sign.pkcs12 import load_pkcs12_keystore  # ❌ Import error
from pyhanko.sign import signers

# Load certificate manually
with open('cert.pfx', 'rb') as f:
    key, cert, additional_certs = load_pkcs12_keystore(f.read(), password)

# Create signer manually
signer = signers.SimpleSigner(
    signing_cert=cert,
    signing_key=key,
    cert_registry=signers.SimpleCertificateStore(additional_certs),  # ❌ Doesn't exist
    signature_mechanism=signers.PdfSignatureMetadata(field_name="Signature1")
)

# Sign with old API
with open('input.pdf', 'rb') as inf, open('output.pdf', 'wb') as outf:
    signers.sign_pdf(inf, signer=signer, output=outf)  # ❌ Wrong parameters
'''
    print(old_api)
    
    print("\n✅ NEW API (what the fixed script uses):")
    new_api = '''
# This is the modern approach:
from pyhanko.sign import signers
from pyhanko.pdf_utils.incremental_writer import IncrementalPdfFileWriter
from pyhanko.pdf_utils.reader import PdfFileReader

# Load certificate using built-in method
signer = signers.SimpleSigner.load_pkcs12(
    pfx_file='cert.pfx',
    passphrase=b'password'
)

# Sign with new API
with open('input.pdf', 'rb') as inf, open('output.pdf', 'wb') as outf:
    pdf_reader = PdfFileReader(inf)
    writer = IncrementalPdfFileWriter(outf, pdf_reader)
    sig_field_spec = signers.PdfSignatureMetadata(field_name='Signature1')
    signers.sign_pdf(writer, sig_field_spec, signer)
'''
    print(new_api)


def key_differences():
    """
    Explain key differences between old and new API
    """
    print("\nKEY DIFFERENCES:")
    print("=" * 40)
    
    differences = [
        "1. Certificate Loading:",
        "   OLD: Manual PKCS12 loading with load_pkcs12_keystore()",
        "   NEW: Built-in SimpleSigner.load_pkcs12() method",
        "",
        "2. Certificate Store:",
        "   OLD: SimpleCertificateStore class (removed)",
        "   NEW: Handled automatically by SimpleSigner.load_pkcs12()",
        "",
        "3. PDF Signing:",
        "   OLD: signers.sign_pdf(input_stream, signer=signer, output=output_stream)",
        "   NEW: signers.sign_pdf(writer, sig_field_spec, signer)",
        "",
        "4. PDF Handling:",
        "   OLD: Direct file streams",
        "   NEW: PdfFileReader + IncrementalPdfFileWriter",
        "",
        "5. Error Handling:",
        "   OLD: Multiple import attempts for PKCS12 loading",
        "   NEW: Single method with built-in error handling",
    ]
    
    for diff in differences:
        print(diff)


def why_pkcs12_not_pkcs11():
    """
    Explain the difference between PKCS#11 and PKCS#12
    """
    print("\nPKCS#11 vs PKCS#12 - Why PKCS#12?")
    print("=" * 40)
    
    explanation = [
        "PKCS#11:",
        "- Standard for Hardware Security Modules (HSMs)",
        "- Smart cards, USB tokens, hardware devices",
        "- Used for accessing keys stored in hardware",
        "- Requires special drivers and hardware",
        "",
        "PKCS#12:",
        "- Standard for storing private keys + certificates in files",
        "- File formats: .pfx, .p12",
        "- Software-based certificate storage",
        "- What we're using in this script",
        "",
        "Our Use Case:",
        "- We have a .pfx file (PKCS#12 format)",
        "- Contains private key + certificate in one file",
        "- Password-protected",
        "- Perfect for software-based PDF signing",
        "",
        "That's why we use PKCS#12, not PKCS#11!"
    ]
    
    for line in explanation:
        print(line)


def main():
    """Main function to run all examples"""
    modern_pyhanko_example()
    old_vs_new_api()
    key_differences()
    why_pkcs12_not_pkcs11()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("The digitally_sign_pdf.py script has been updated to use")
    print("the modern pyhanko API with SimpleSigner.load_pkcs12().")
    print("This fixes all the import errors and API compatibility issues.")
    print("=" * 60)


if __name__ == "__main__":
    main()
