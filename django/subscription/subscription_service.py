import logging
from django.conf import settings
from utils import DateUtil, RedisUtils
from utils.constants import AdminDBColls, SubscriptionStatus
from utils.mongo import MongoUtility

logger = logging.getLogger('application')

ONE_DAY_IN_MS = 86400000


class SubscriptionService:
    """
    Service class for managing company subscriptions in Redis cache.
    Uses a single Redis connection for better performance.
    """

    # Class-level Redis connection
    _redis = None
    _cache_expiry = 3600

    @classmethod
    def get_redis(cls):
        """Get or create the Redis connection"""
        if cls._redis is None:
            cls._redis = RedisUtils(settings.REDIS_SAAS_DB_URL)
        return cls._redis

    @classmethod
    def get_company_subscriptions(cls, company_id):
        """
        Get processed company subscriptions from Redis cache or load from DB if not present.
        Returns a dictionary with optimized subscription data.

        The data structure is:
        {
            'product_ids': [1, 2, 3],  # List of product IDs the company has access to
            'feature_ids': ['feature1', 'feature2'],  # List of feature IDs the company has access to
            'feature_values': {'feature1': 10, 'feature2': 20},  # Feature values for numeric features
            'products': {  # Subscriptions organized by product ID
                1: {subscription_data},
                2: {subscription_data},
                3: {subscription_data}
            }
        }
        """
        redis = cls.get_redis()
        cache_key = f"company_subs:{company_id}"
        subscription_data = redis.get_data(cache_key)

        if not subscription_data:
            # If not in cache, fetch from DB and process into an optimized format
            db = MongoUtility()
            query = {
                'company_id': company_id,
                'status_id': {'$in': [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]}
            }
            df = {'_id': 0, 'features': 0}
            subscriptions = list(db.find(AdminDBColls.SUBSCRIPTIONS, query, df))

            # Process into an optimized dictionary format
            subscription_data = {
                'product_ids': set(),
                'feature_ids': set(),
                'feature_values': {},  # For features with numeric values
                'products': {}  # Subscriptions organized by product ID
            }

            # Import PlanService here to avoid circular imports
            from .plan_service import PlanService

            for sub in subscriptions:
                product_id = sub['product_id']
                plan_id = sub.get('plan_id')

                subscription_data['product_ids'].add(product_id)

                # Store the subscription under its product ID
                subscription_data['products'][product_id] = sub

                # Get features from the plan instead of the subscription
                plan = PlanService.get_plan(plan_id) if plan_id else None
                if not plan:
                    continue

                # Process plan features
                for feature in plan.get('features', []):
                    if feature.get('ival'):
                        # Only include enabled features
                        continue

                    feature_id = feature['id']
                    subscription_data['feature_ids'].add(feature_id)
                    subscription_data['feature_values'][feature_id] = feature['ival']

            if subscription_data['product_ids']:
                # Convert sets to lists for JSON serialization
                subscription_data['product_ids'] = list(subscription_data['product_ids'])
                subscription_data['feature_ids'] = list(subscription_data['feature_ids'])

                # Cache the processed data (1 hour expiry)
                redis.set_data(cache_key, subscription_data, cls._cache_expiry)

        return subscription_data

    @classmethod
    def invalidate_company_subscriptions_cache(cls, company_id):
        """
        Invalidate the company subscriptions cache.
        Call this method whenever a subscription is created, updated, or deleted.
        """
        redis = cls.get_redis()
        cache_key = f"company_subs:{company_id}"
        redis.delete_data(cache_key)
        logger.info(f"Cache invalidated for company subscriptions {company_id}")

    @classmethod
    def get_product_subscription(cls, company_id, product_id):
        """
        Get the subscription data for a specific product for a company.

        Args:
            company_id (str): The company ID
            product_id (int): The product ID

        Returns:
            dict: The subscription data for the product or None if not found
        """
        subscription_data = cls.get_company_subscriptions(company_id)
        return subscription_data.get('products', {}).get(str(product_id))

    @classmethod
    def get_product_features(cls, company_id, product_id):
        """
        Get the features for a specific product subscription.

        Args:
            company_id (str): The company ID
            product_id (int): The product ID

        Returns:
            list: List of features for the product subscription
        """
        subscription = cls.get_product_subscription(company_id, product_id)
        if not subscription:
            return []

        plan_id = subscription.get('plan_id')
        if not plan_id:
            return []

        # Import PlanService here to avoid circular imports
        from .plan_service import PlanService

        plan = PlanService.get_plan(plan_id)
        if not plan:
            return []

        return plan.get('features', [])

    @classmethod
    def has_product_access(cls, company_id, product_id):
        """Check if company has access to a specific product"""
        subscription_data = cls.get_company_subscriptions(company_id)
        return product_id in subscription_data['product_ids']

    @classmethod
    def get_active_subscriptions(cls, company_id):
        """
        Get all active subscriptions for a company.
        This is a convenience method for backward compatibility.

        Args:
            company_id (str): The company ID

        Returns:
            list: List of active subscriptions for the company
        """
        subscription_data = cls.get_company_subscriptions(company_id)
        return list(subscription_data.get('products', {}).values())

    @classmethod
    def has_feature_access(cls, company_id, feature_id, min_value=None):
        """
        Check if company has access to a specific feature.
        Optionally check if the feature value meets a minimum requirement.
        """
        subscription_data = cls.get_company_subscriptions(company_id)

        if feature_id not in subscription_data['feature_ids']:
            return False

        if min_value is not None:
            return subscription_data['feature_values'].get(feature_id, 0) >= min_value

        return True

    @classmethod
    def get_subscription_by_id(cls, subscription_id):
        """
        Get a subscription by its ID.

        Args:
            subscription_id (str): The subscription ID

        Returns:
            dict: The subscription data or None if not found
        """
        db = MongoUtility()
        return db.find(AdminDBColls.SUBSCRIPTIONS, {'id': subscription_id}, {'_id': 0}, find_one=True)

    @classmethod
    def get_subscription_status(cls, subscription):
        """
        Get detailed status information for a subscription.

        Args:
            subscription (dict): The subscription data

        Returns:
            dict: Detailed status information including:
                - is_active (bool): Whether the subscription is active
                - status (str): The subscription status (ACTIVE, TRIAL, EXPIRED, etc.)
                - remaining_days (int): Remaining days for the subscription (if applicable)
                - start_date (int): The subscription start date (timestamp)
                - end_date (int): The subscription end date (timestamp)
        """
        if not subscription:
            return {
                'is_active': False,
                'status': 'NOT_FOUND',
                'remaining_days': 0,
                'start_date': None,
                'end_date': None
            }

        # Get current timestamp
        current_time = DateUtil.get_current_timestamp()

        # Extract subscription data
        status_id = subscription.get('status_id')
        status = subscription.get('status')
        start_date = subscription.get('start_date')
        end_date = subscription.get('end_date')
        current_start = subscription.get('current_start')
        current_end = subscription.get('current_end')

        # Check if subscription is active based on status and dates
        is_active = False
        remaining_days = 0
        final_start, final_end = current_start, current_end

        if status_id == SubscriptionStatus.TRIAL.value:
            final_start, final_end = start_date, end_date
            # For trial subscriptions, use end_date
            if end_date and end_date > current_time:
                is_active = True
                remaining_days = max(0, (end_date - current_time) // ONE_DAY_IN_MS)
        elif status_id == SubscriptionStatus.ACTIVE.value:
            # For active subscriptions, use current_end
            if current_end and current_end > current_time:
                is_active = True
                remaining_days = max(0, (current_end - current_time) // ONE_DAY_IN_MS)
            elif not current_end:
                # If no current_end, subscription is considered active
                is_active = True

        return {
            'is_active': is_active,
            'status': status,
            'remaining_days': remaining_days,
            'start_date': final_start,
            'end_date': final_end
        }

    @classmethod
    def is_subscription_active(cls, subscription_id):
        """
        Check if a subscription is active based on its ID.

        Args:
            subscription_id (str): The subscription ID

        Returns:
            dict: Detailed status information including:
                - is_active (bool): Whether the subscription is active
                - status (str): The subscription status (ACTIVE, TRIAL, EXPIRED, etc.)
                - remaining_days (int): Remaining days for the subscription (if applicable)
                - start_date (int): The subscription start date (timestamp)
                - end_date (int): The subscription end date (timestamp)
        """
        subscription = cls.get_subscription_by_id(subscription_id)
        return cls.get_subscription_status(subscription)

    @classmethod
    def is_product_subscription_active(cls, company_id, product_id):
        """
        Check if a product subscription is active for a company.

        Args:
            company_id (str): The company ID
            product_id (int): The product ID

        Returns:
            dict: Detailed status information including:
                - is_active (bool): Whether the subscription is active
                - status (str): The subscription status (ACTIVE, TRIAL, EXPIRED, etc.)
                - remaining_days (int): Remaining days for the subscription (if applicable)
                - start_date (int): The subscription start date (timestamp)
                - end_date (int): The subscription end date (timestamp)
        """
        subscription = cls.get_product_subscription(company_id, product_id)
        return cls.get_subscription_status(subscription)

    @classmethod
    def get_trial_remaining_days(cls, subscription):
        """
        Calculate remaining days for a trial subscription based on the end_date.

        Args:
            subscription (dict): The subscription data

        Returns:
            dict: Trial information including:
                - remaining_days (int): Remaining days for the trial subscription
                - is_active (bool): Whether the trial is active
        """
        if not subscription:
            return {
                'remaining_days': 0,
                'is_active': False
            }

        # Check if subscription is a trial
        if subscription.get('status_id') != SubscriptionStatus.TRIAL.value:
            return {
                'remaining_days': 0,
                'is_active': False
            }

        # Get current timestamp
        current_time = DateUtil.get_current_timestamp()

        # Get end date
        end_date = subscription.get('end_date')

        # If no end date or end date is in the past, trial is not active
        is_active = False
        remaining_days = 0

        if end_date and end_date > current_time:
            is_active = True
            # Calculate remaining days
            remaining_days = max(0, (end_date - current_time) // ONE_DAY_IN_MS)

        return {
            'remaining_days': remaining_days,
            'is_active': is_active
        }
