import logging
from django.conf import settings
from utils import RedisUtils
from utils.constants import AdminDBColls
from utils.mongo import MongoUtility

logger = logging.getLogger('application')


class PlanService:
    """
    Service class for managing subscription plans in Redis cache.
    Provides methods to load, cache, and retrieve plans with key format 'plan:<plan_id>'.
    Uses a single Redis connection for better performance.
    """

    # Class-level Redis connection
    _redis = None
    _cache_expiry = None

    @classmethod
    def get_redis(cls):
        """Get or create the Redis connection"""
        if cls._redis is None:
            cls._redis = RedisUtils(settings.REDIS_SAAS_DB_URL)
        return cls._redis

    @classmethod
    def get_plan(cls, plan_id):
        """
        Get a plan from Redis cache or load from DB if not present.
        Returns the plan data or None if not found.

        Args:
            plan_id (str): The ID of the plan to retrieve

        Returns:
            dict: The plan data or None if not found
        """
        redis = cls.get_redis()
        cache_key = f"plan:{plan_id}"
        plan_data = redis.get_data(cache_key)

        if not plan_data:
            # If not in cache, fetch from DB
            db = MongoUtility()
            plan_data = db.find(AdminDBColls.PLANS, {'id': plan_id}, find_one=True)

            if plan_data:
                # Cache the plan data (No expiry)
                redis.set_data(cache_key, plan_data, cls._cache_expiry)
            else:
                logger.warning(f"Plan with ID {plan_id} not found in database")
                return None

        return plan_data

    @classmethod
    def cache_plan(cls, plan_id):
        """
        Force load a plan from the database and cache it in Redis.
        Useful for ensuring the cache is up-to-date after plan updates.

        Args:
            plan_id (str): The ID of the plan to cache

        Returns:
            dict: The plan data or None if not found
        """
        db = MongoUtility()
        plan_data = db.find(AdminDBColls.PLANS, {'id': plan_id}, find_one=True)

        if plan_data:
            redis = cls.get_redis()
            cache_key = f"plan:{plan_id}"
            # Cache the plan data (No expiry)
            redis.set_data(cache_key, plan_data, cls._cache_expiry)
            return plan_data
        else:
            logger.warning(f"Plan with ID {plan_id} not found in database")
            return None

    @classmethod
    def invalidate_plan_cache(cls, plan_id):
        """
        Invalidate the plan cache for a specific plan.
        Call this method whenever a plan is updated or deleted.

        Args:
            plan_id (str): The ID of the plan to invalidate in cache
        """
        redis = cls.get_redis()
        cache_key = f"plan:{plan_id}"
        redis.delete_data(cache_key)
        logger.info(f"Cache invalidated for plan {plan_id}")

    @classmethod
    def cache_all_plans(cls, product_id=None):
        """
        Cache all active plans in Redis.
        Optionally filter by product_id.

        Args:
            product_id (int, optional): Filter plans by product ID

        Returns:
            int: Number of plans cached
        """
        db = MongoUtility()
        redis = cls.get_redis()

        # Build query to get active plans
        query = {'is_active': True}
        if product_id is not None:
            query['product_id'] = product_id

        # Get all active plans
        plans = list(db.find(AdminDBColls.PLANS, query, sort=[('index', 1)]))

        # Cache each plan
        for plan in plans:
            plan_id = plan['id']
            cache_key = f"plan:{plan_id}"
            # Cache the plan data (No expiry)
            redis.set_data(cache_key, plan, cls._cache_expiry)

        logger.info(f"Cached {len(plans)} plans in Redis")
        return plans

    @classmethod
    def get_plans_by_product(cls, product_id, use_cache=True):
        """
        Get all active plans for a specific product.

        Args:
            product_id (int): The product ID to filter plans by
            use_cache (bool): Whether to use cached plans or force DB query

        Returns:
            list: List of plans for the product
        """
        if not use_cache:
            # Force DB query
            db = MongoUtility()
            query = {
                'product_id': product_id,
                'is_active': True
            }
            return list(db.find(AdminDBColls.PLANS, query, sort=[('index', 1)]))

        # Try to get from cache first
        redis = cls.get_redis()
        # Get all keys matching the pattern
        keys = redis.redis_conn.keys("plan:*")

        if not keys:
            # No plans in cache, load from DB and cache them
            return cls.cache_all_plans(product_id)

        # Get all plans from cache
        plans = []
        for key in keys:
            plan_data = redis.get_data(key)
            if plan_data and plan_data.get('product_id') == product_id and plan_data.get('is_active', False):
                plans.append(plan_data)

        # If no plans found in cache for this product, try loading from DB
        if not plans:
            return cls.cache_all_plans(product_id)

        return plans
